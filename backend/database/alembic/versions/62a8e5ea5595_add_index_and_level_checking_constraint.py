"""add index and level checking constraint

Revision ID: 62a8e5ea5595
Revises: 46e38d74e1d0
Create Date: 2025-07-15 16:56:01.463637

"""

from typing import Sequence, Union

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "62a8e5ea5595"
down_revision: Union[str, None] = "46e38d74e1d0"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index("idx_assessment_questions_assessment_id", "assessment_questions", ["assessment_id"], unique=False)
    op.create_index("idx_assessment_questions_question_id", "assessment_questions", ["question_id"], unique=False)
    op.create_index("idx_assessment_skills_skill_id", "assessment_skills", ["skill_id"], unique=False)
    op.create_index("idx_questions_level", "questions", ["level"], unique=False)
    op.create_index("idx_questions_skill_id", "questions", ["skill_id"], unique=False)
    op.create_index("idx_quiz_creation_logs_assessment_id", "quiz_creation_logs", ["assessment_id"], unique=False)
    op.create_index("idx_sessions_assessment_id", "sessions", ["assessment_id"], unique=False)
    op.create_index("idx_sessions_code", "sessions", ["code"], unique=False)
    op.create_index("idx_sessions_user_id", "sessions", ["user_id"], unique=False)
    op.create_index("idx_user_answers_question_id", "user_answers", ["question_id"], unique=False)
    op.create_index("idx_user_answers_session_id", "user_answers", ["session_id"], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index("idx_user_answers_session_id", table_name="user_answers")
    op.drop_index("idx_user_answers_question_id", table_name="user_answers")
    op.drop_index("idx_sessions_user_id", table_name="sessions")
    op.drop_index("idx_sessions_code", table_name="sessions")
    op.drop_index("idx_sessions_assessment_id", table_name="sessions")
    op.drop_index("idx_quiz_creation_logs_assessment_id", table_name="quiz_creation_logs")
    op.drop_index("idx_questions_skill_id", table_name="questions")
    op.drop_index("idx_questions_level", table_name="questions")
    op.drop_index("idx_assessment_skills_skill_id", table_name="assessment_skills")
    op.drop_index("idx_assessment_questions_question_id", table_name="assessment_questions")
    op.drop_index("idx_assessment_questions_assessment_id", table_name="assessment_questions")
    # ### end Alembic commands ###
