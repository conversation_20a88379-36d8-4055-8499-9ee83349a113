"""
Database package initialization.

This package contains all database-related modules including:
- SQLAlchemy models
- Database configuration
- Migration utilities
- Alembic configuration (to be added)
"""

from .db import Base, SessionLocal, engine, get_db, get_db_context, init_db
from .models import (
    Assessment,
    AssessmentQuestion,
    AssessmentSkill,
    Question,
    QuizCreationLog,
    Session,
    Skill,
    User,
    UserAnswer,
)

__all__ = [
    # Database configuration
    "Base",
    "engine",
    "SessionLocal",
    "get_db",
    "get_db_context",
    "init_db",
    # Models
    "User",
    "Skill",
    "Question",
    "Assessment",
    "AssessmentSkill",
    "AssessmentQuestion",
    "Session",
    "UserAnswer",
    "QuizCreationLog",
]
