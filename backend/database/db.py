"""
SQLAlchemy database configuration and session management.

This module provides the SQLAlchemy engine, session factory, and base model
for all database operations. It's configured to work with Docker service names.
"""

import os
from contextlib import contextmanager
from typing import Generator

from app.config.config import DATABASE_URL
from sqlalchemy import create_engine, event
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import Session, sessionmaker

try:
    from app.utils.logger import debug as log_debug
    from app.utils.logger import error as log_error
    from app.utils.logger import info as log_info
except ImportError:
    # Fallback for when logger is not available
    def log_info(msg, **kwargs):
        print(f"INFO: {msg}")

    def log_error(msg, **kwargs):
        print(f"ERROR: {msg}")


# Create SQLAlchemy engine
engine = create_engine(
    DATABASE_URL,
    # Connection pool settings
    pool_size=10,
    max_overflow=20,
    pool_pre_ping=True,
    pool_recycle=3600,
    # For testing environments, you might want to use NullPool
    # poolclass=NullPool,
    echo=os.getenv("SQL_ECHO", "false").lower() == "true",  # Enable SQL logging if needed
)

# Create session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Create base class for all models
Base = declarative_base()


def get_db() -> Generator[Session, None, None]:
    """
    Dependency function to get database session.

    Yields:
        Session: SQLAlchemy database session
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


@contextmanager
def get_db_context() -> Generator[Session, None, None]:
    """
    Context manager for database sessions.

    Yields:
        Session: SQLAlchemy database session
    """
    db = SessionLocal()
    try:
        yield db
        db.commit()
    except Exception as e:
        db.rollback()
        log_error(f"Database session error: {e}")
        raise
    finally:
        db.close()


def init_db():
    """
    Initialize database tables.
    Note: This should only be used in development.
    For production, use Alembic migrations.
    """
    try:
        # Create all tables
        Base.metadata.create_all(bind=engine)
        log_info("Database tables initialized successfully")
    except Exception as e:
        log_error(f"Error initializing database: {e}")
        raise


# Event listeners for connection logging
@event.listens_for(engine, "connect")
def on_connect(dbapi_connection, connection_record):
    """Log database connections."""
    log_info("Database connection established")


@event.listens_for(engine, "checkout")
def on_checkout(dbapi_connection, connection_record, connection_proxy):
    """Log connection checkout from pool."""
    log_debug("Database connection checked out from pool")


@event.listens_for(engine, "checkin")
def on_checkin(dbapi_connection, connection_record):
    """Log connection checkin to pool."""
    log_debug("Database connection checked in to pool")
