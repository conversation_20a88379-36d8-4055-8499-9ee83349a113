"""
Quiz-related API routes for the quiz/assessment management system.
"""

import os
import random
from typing import Optional

from database.db import get_db_context
from fastapi import APIRouter, Depends, HTTPException, Query, status
from pydantic import BaseModel

from ...models.assessment_manager import (
    fetch_dynamic_questions_excluding_fixed,
    fetch_questions_for_fixed_assessment,
    get_assessment_details,
    get_session_and_assessment_details_by_code,
)
from ...models.db_manager import (
    insert_user_data,
)
from ...models.quiz_manager import (
    db_autostart_session_by_code,
    db_count_questions_in_fixed_assessment,
    db_get_assessment_details_by_id,
    db_get_assessment_total_questions,
    db_get_question_by_id,
    db_get_skill_ids_for_assessment,
    db_get_sum_of_scores_for_session,
    db_get_user_external_id_by_internal_id,
    db_upsert_user_answer,
    fetch_attempted_question_ids,
    fetch_attempted_question_ids_by_session,
    fetch_final_questions,
    fetch_questions_for_skills,
    get_final_question_ids,
    get_questions_for_check,
)
from ...models.sessions_manager import (
    get_session_response_data,
    get_session_user_id,
    validate_session_and_user,
    validate_session_code_format,
    validate_session_for_question,
)
from ...utils.api_response import (
    error_response,
    raise_http_exception,
    success_response,
)
from ...utils.db_utils import (
    safe_json_dumps,
    safe_json_loads,
)
from ...utils.logger import (
    debug,
    error,
    info,
    warning,
)
from ...utils.performance_utils import (
    calculate_question_score,
)
from ...utils.rate_limiter import rate_limiter
from ...utils.session_utils import (
    get_session_details,
)


# Define Pydantic models for request validation
class AnswerRequest(BaseModel):
    user_id: str
    question_id: str  # This is que_id from questions table
    answer: str
    session_code: str  # Changed from quiz_code
    time_taken: Optional[int] = None  # Time taken in seconds


class UserCheckRequest(BaseModel):
    user_id: str


class TaskResponse(BaseModel):
    """Response model for task submission"""

    task_id: str
    status: str
    message: str


quiz_router = APIRouter()


# =============================================================================
# CHECK AND SAVE ANSWER API ENDPOINT AND HELPER FUNCTIONS
# =============================================================================


def _determine_result_status(answer: str, is_correct: bool) -> str:
    """Determine result status based on answer and correctness."""
    lowercased_answer = answer.lower()
    if lowercased_answer == "timeout":
        return "Timeout"
    elif is_correct:
        return "Correct"
    else:
        return "Incorrect"


def _save_to_legacy_table(
    user_id: str,
    legacy_topic: str,
    question_level: str,
    legacy_quiz_type: str,
    question_id_int: int,
    question_text: str,
    question_options_json: str,
    question_correct_answer_key: str,
    answer: str,
    result_status: str,
    score: float,
):
    """Save result to legacy user_assessment table for backward compatibility."""
    insert_user_data(
        {
            "user_id": user_id,
            "topic": legacy_topic,
            "level": question_level,
            "quiz_type": legacy_quiz_type,
            "que_id": question_id_int,
            "question": question_text,
            "options": safe_json_loads(question_options_json, {}),
            "correct_answer": question_correct_answer_key,
            "user_answer": "None" if answer.lower() == "timeout" else answer,
            "result": result_status,
            "score": score,
        }
    )


def _save_to_user_answers_table(
    session_code: str,
    question_id_int: int,
    answer: str,
    is_correct: bool,
    score: float,
    time_taken: Optional[int],
):
    """Save result to user_answers table by calling the data access layer."""
    session_details = get_session_details(session_code)
    if not session_details:
        raise ValueError(f"Session code {session_code} not found during save_result_to_db")

    internal_session_id = session_details["session_id"]

    # Use SQLAlchemy database context manager for the upsert operation
    with get_db_context() as db:
        # Call the data access function to perform the upsert
        db_upsert_user_answer(db, internal_session_id, question_id_int, answer, is_correct, score, time_taken)
        db.commit()


def save_result_to_db(
    user_id: str,
    question_id_int: int,
    answer: str,
    is_correct: bool,
    session_code: str,
    time_taken: Optional[int],
    legacy_topic: str,
    legacy_quiz_type: str,
    question_level: str,
    question_text: str,
    question_options_json: str,
    question_correct_answer_key: str,
):
    """Save quiz answer results to the database using the new schema."""
    try:
        # Determine result status
        result_status = _determine_result_status(answer, is_correct)

        # Calculate score
        score = calculate_question_score(question_level, result_status.lower())

        # Save to legacy table for backward compatibility
        _save_to_legacy_table(
            user_id,
            legacy_topic,
            question_level,
            legacy_quiz_type,
            question_id_int,
            question_text,
            question_options_json,
            question_correct_answer_key,
            answer,
            result_status,
            score,
        )

        # Save to user_answers table
        _save_to_user_answers_table(session_code, question_id_int, answer, is_correct, score, time_taken)

        debug("Answer saved successfully to database")

    except Exception as e:
        error("Error saving result", exception=e)
        raise


def _check_answer_correctness(answer: str, question: dict) -> tuple[bool, str, str]:
    """Check if answer is correct and return correctness, answer key, and answer value."""
    correct_answer_key = question["answer"]
    correct_answer_value = safe_json_loads(question["options"], {}).get(correct_answer_key, "")
    is_correct = answer.lower() == correct_answer_key.lower()

    return is_correct, correct_answer_key, correct_answer_value


def _find_question(question_topic_identifier: str, question_id: str) -> dict:
    """Find question by topic and ID, with fallback to ID-only lookup using the data access layer."""
    # First try the original method (for backward compatibility)
    question = get_questions_for_check(question_topic_identifier, question_id)

    # If not found, use the new, clean data access function for the fallback
    if not question:
        try:
            with get_db_context() as db:
                question = db_get_question_by_id(db, int(question_id))
        except (ValueError, TypeError):
            # Handle cases where question_id is not a valid integer
            question = None

    if not question:
        raise_http_exception(status_code=404, detail="Question not found")

    return question


def check_and_save_answer(
    user_id: str,
    question_id: str,
    answer: str,
    session_code: str,
    time_taken: Optional[int] = None,
):
    """
    Check the given answer and save the result to the database.
    This function is called internally by the endpoint.
    """
    try:
        # Validate session and user
        normalized_session_code, session_details, validated_user_id = validate_session_and_user(session_code, user_id)

        # Extract topic identifier from assessment name
        assessment_name = session_details["assessment_name"]
        question_topic_identifier = assessment_name.replace(" Assessment", "")

        # Find the question
        question = _find_question(question_topic_identifier, question_id)

        # Check answer correctness
        is_correct, correct_answer_key, correct_answer_value = _check_answer_correctness(answer, question)

        # Save result to database
        quiz_type = "assessment"  # Always use 'assessment' instead of 'mock' or 'final'
        save_result_to_db(
            user_id=validated_user_id,
            question_id_int=question["que_id"],
            answer=answer,
            is_correct=is_correct,
            session_code=normalized_session_code,
            time_taken=time_taken,
            legacy_topic=question_topic_identifier,
            legacy_quiz_type=quiz_type,
            question_level=question["level"],
            question_text=str(question["question"]),
            question_options_json=safe_json_dumps(question["options"]),
            question_correct_answer_key=correct_answer_key,
        )

        # Get response data
        response_data = get_session_response_data(
            session_details, is_correct, correct_answer_key, correct_answer_value, normalized_session_code
        )

        return success_response(data=response_data, message="Answer checked and saved successfully")

    except HTTPException:
        raise
    except Exception as e:
        error("Error in check_and_save_answer_internal", exception=e)
        raise HTTPException(status_code=500, detail={"error": str(e)})


@quiz_router.post("/check_and_save_answer")
def check_and_save_answer_endpoint(
    # Contains session_code now
    request: AnswerRequest,
    _: None = Depends(rate_limiter),
):
    """
    Checks and saves the user's answer for a specific quiz question using session_code.
    """
    # The internal check_and_save_answer function now handles fetching session details
    result = check_and_save_answer(
        user_id=request.user_id,
        question_id=request.question_id,
        answer=request.answer,
        session_code=request.session_code,
        time_taken=request.time_taken,
    )
    return result


# =============================================================================
# GET QUESTIONS API ENDPOINT AND HELPER FUNCTIONS
# =============================================================================


def _filter_fixed_assessment_questions(all_questions, attempted_questions, difficulty, retake):
    """Filter questions for fixed assessments."""
    filtered_questions = []
    for question in all_questions:
        if question["que_id"] in attempted_questions and not retake:
            continue
        if difficulty != "all" and question["level"] != difficulty:
            continue
        filtered_questions.append(question)
    return filtered_questions


def _determine_questions_count(
    db, is_fixed_assessment: bool, is_final: bool, filtered_questions: list, assessment_id: int
):
    """Determine the number of questions to return based on assessment type."""
    if is_fixed_assessment or is_final:
        return len(filtered_questions)

    # For mock/dynamic quizzes, call the data access layer
    total_questions = db_get_assessment_total_questions(db, assessment_id)
    if total_questions is not None:
        return total_questions

    # Fallback to environment variable if DB call fails or returns nothing
    mock_q_count_env = os.getenv("MOCK_QUESTION_COUNT", "10")
    try:
        return int(mock_q_count_env)
    except ValueError:
        return 10  # Final fallback


def _select_balanced_questions_by_skill(filtered_questions, num_questions_to_return):
    """Select questions with balanced distribution across skills."""
    # Group questions by skill
    questions_by_skill = {}
    for q in filtered_questions:
        skill_id = q.get("skill_id")
        if skill_id:
            questions_by_skill.setdefault(skill_id, []).append(q)

    if not questions_by_skill:
        return random.sample(filtered_questions, num_questions_to_return)

    # Calculate distribution
    questions_per_skill = num_questions_to_return // len(questions_by_skill)
    remainder = num_questions_to_return % len(questions_by_skill)

    selected_questions = []
    for skill_id, questions in questions_by_skill.items():
        skill_question_count = questions_per_skill + (1 if remainder > 0 else 0)
        remainder = max(0, remainder - 1)

        if len(questions) > skill_question_count:
            selected_questions.extend(random.sample(questions, skill_question_count))
        else:
            selected_questions.extend(questions)

    # Fill remaining slots if needed
    if len(selected_questions) < num_questions_to_return:
        remaining_questions = [q for q in filtered_questions if q not in selected_questions]
        additional_needed = num_questions_to_return - len(selected_questions)
        if remaining_questions and additional_needed > 0:
            additional_count = min(len(remaining_questions), additional_needed)
            selected_questions.extend(random.sample(remaining_questions, additional_count))

    return selected_questions


def _select_questions_for_response(filtered_questions, num_questions_to_return, is_fixed_assessment, skill_ids):
    """Select questions for response based on assessment type and requirements."""
    if is_fixed_assessment:
        return filtered_questions

    if len(filtered_questions) <= num_questions_to_return:
        return filtered_questions

    # For dynamic assessments with multiple skills, balance distribution
    if skill_ids and len(skill_ids) > 1:
        return _select_balanced_questions_by_skill(filtered_questions, num_questions_to_return)
    else:
        return random.sample(filtered_questions, num_questions_to_return)


def _get_questions_for_assessment(
    db, assessment_id: int, is_fixed_assessment: bool, question_topic_identifier: str, is_final: bool
) -> list:
    """Get all available questions for the assessment by orchestrating data access calls."""
    info(
        f"_get_questions_for_assessment called with assessment_id={assessment_id}, is_fixed_assessment={is_fixed_assessment}"
    )

    if is_fixed_assessment:
        # This function is assumed to be another helper, its internal logic doesn't matter here
        info(f"Fetching questions for fixed assessment {assessment_id}")
        return fetch_questions_for_fixed_assessment(assessment_id)
    else:
        # For dynamic assessments, get skill IDs from the data access layer
        skill_ids = db_get_skill_ids_for_assessment(db, assessment_id)
        info(f"Dynamic assessment {assessment_id} has skill_ids: {skill_ids}")

        # The rest is business logic based on the result
        if skill_ids:
            info(f"Fetching questions for skills: {skill_ids}")
            return fetch_questions_for_skills(skill_ids, exclude_fixed_assessment_questions=True)
        else:
            if is_final:
                info(f"Fetching final questions for topic: {question_topic_identifier}")
                return fetch_final_questions(question_topic_identifier)
            else:
                info(f"Fetching dynamic questions excluding fixed for topic: {question_topic_identifier}")
                return fetch_dynamic_questions_excluding_fixed(question_topic_identifier)


def _get_total_questions_count(cur, assessment_id: int, is_fixed_assessment: bool) -> Optional[int]:
    """Get total questions count by calling the appropriate data access function."""
    with get_db_context() as db:
        if is_fixed_assessment:
            return db_count_questions_in_fixed_assessment(db, assessment_id)
        else:
            # Reuse the function we created earlier for dynamic assessments
            return db_get_assessment_total_questions(db, assessment_id)


def _calculate_current_score(session_id: int) -> float:
    """Calculate current score by calling the data access layer and handling exceptions."""
    try:
        with get_db_context() as db:
            # Call the data access function to get the raw score
            total_score = db_get_sum_of_scores_for_session(db, session_id)
            # Business logic: handle the case where no score exists and return 0.0
            return total_score if total_score is not None else 0.0
    except Exception as e:
        # The exception handling remains part of the business logic
        error(f"Error calculating current score for session {session_id}: {e}")
        return 0.0


def _format_question_response(
    next_question: dict,
    session_details: dict,
    assessment_details: dict,
    attempted_questions: list,
    total_questions: int,
    current_score: float,
) -> dict:
    """Format the response data for the next question."""
    # Filter out sensitive fields from question data
    filtered_question = {
        "que_id": next_question["que_id"],
        "topic": next_question["topic"],
        "level": next_question["level"],
        "question": next_question["question"],
        "options": next_question["options"],
    }

    # Get question selection mode
    question_selection_mode = "dynamic"
    if assessment_details and "question_selection_mode" in assessment_details:
        question_selection_mode = assessment_details["question_selection_mode"]

    return {
        "quiz_type": "final" if session_details["is_final"] else "mock",
        "quiz_name": session_details["assessment_name"],
        "topic": session_details["assessment_name"].replace(" Mock Assessment", "").replace(" Final Assessment", ""),
        "question": filtered_question,
        "remaining_time_seconds": session_details.get("remaining_time_seconds", None),
        "attempted_questions_count": len(attempted_questions),
        "total_questions": total_questions,
        "current_score": current_score,
        "question_selection_mode": question_selection_mode,
    }


def format_response(
    quiz_type,
    assessment_full_name,
    user_facing_topic,
    selected_questions,
    remaining_time_seconds=None,
    attempted_questions_count=None,
    total_questions=None,
):
    """Format response JSON."""
    response = {
        "quiz_type": quiz_type,
        # e.g. "MyAssessment_timestamp Mock Assessment"
        "quiz_name": assessment_full_name,
        "topic": user_facing_topic,  # Skill description or similar for user context
        "question": [
            {
                "que_id": q["que_id"],
                "question": q["question"],
                "options": q["options"],
                "level": q["level"],
            }
            for q in selected_questions
        ],
    }

    # Add session progress information if available
    if remaining_time_seconds is not None:
        response["remaining_time_seconds"] = remaining_time_seconds

    if attempted_questions_count is not None:
        response["attempted_questions_count"] = attempted_questions_count

    if total_questions is not None:
        response["total_questions"] = total_questions
        response["questions_left"] = max(0, total_questions - (attempted_questions_count or 0))

    return response


def get_questions_by_difficulty(all_questions, difficulty):
    """Filter questions based on the specified difficulty level."""
    return [q for q in all_questions if q["level"] == difficulty]


def filter_questions(
    all_questions,
    attempted,
    final_ids_to_exclude_from_mock,
    is_final,
    difficulty,
    allow_retake=False,
):
    """Filter questions based on quiz type and difficulty."""
    if is_final:
        # For final quizzes, all_questions are already the selected final questions.
        # Filter out attempted ones.
        if difficulty != "all":
            # If a specific difficulty is requested, filter by it
            filtered_by_difficulty = get_questions_by_difficulty(all_questions, difficulty)
            return [q for q in filtered_by_difficulty if q["que_id"] not in attempted]
        else:
            # If all difficulties are requested, just filter out attempted ones
            return [q for q in all_questions if q["que_id"] not in attempted]

    # For mock quizzes
    if difficulty != "all":
        # If a specific difficulty is requested, filter by it
        questions_of_difficulty = get_questions_by_difficulty(all_questions, difficulty)
    else:
        # If all difficulties are requested, use all questions
        questions_of_difficulty = all_questions

    # Filter out attempted questions (if not retake) and questions already in a final set for this topic
    return [
        q
        for q in questions_of_difficulty
        if (allow_retake or q["que_id"] not in attempted) and q["que_id"] not in final_ids_to_exclude_from_mock
    ]


def _filter_questions_by_assessment_type(
    all_questions, attempted_questions, is_fixed_assessment, is_final, difficulty, retake, question_topic_identifier
):
    """Filter questions based on assessment type and parameters."""
    if is_fixed_assessment:
        return _filter_fixed_assessment_questions(all_questions, attempted_questions, difficulty, retake)
    else:
        final_question_ids = get_final_question_ids(question_topic_identifier) if not is_final else []
        return filter_questions(
            all_questions,
            attempted_questions,
            final_question_ids,
            is_final,
            difficulty,
            allow_retake=not is_final and retake,
        )


def get_error_message(is_final, quiz_name_base, difficulty):
    """Generate an appropriate error message."""
    if is_final:
        return f"No more final questions available for assessment: {quiz_name_base}, or you have attempted all."
    else:
        return (
            f"No new '{difficulty}' questions available for assessment: {quiz_name_base}. "
            "This could be because: 1) All questions have been attempted, "
            "2) All questions are assigned to fixed assessments, "
            "or 3) No questions exist for this difficulty level. Try another difficulty or create more questions."
        )


@quiz_router.get("/get_questions/{session_code}")
def get_questions(
    session_code: str,
    user_id: str = Query(...),
    difficulty: str = Query("easy"),
    retake: bool = Query(False),
):
    try:
        decoded_session_code = validate_session_code_format(session_code)

        # Use SQLAlchemy database context for all operations in this request
        with get_db_context() as db:
            # 1. GET INITIAL SESSION & USER DATA
            session_details = get_session_and_assessment_details_by_code(decoded_session_code)
            if not session_details:
                return error_response(message="Invalid or expired session code.", code=status.HTTP_404_NOT_FOUND)

            session_user_id = db_get_user_external_id_by_internal_id(db, session_details["user_id"])
            if not session_user_id:
                raise_http_exception(status_code=404, detail="User not found for this session")

            # Validate user, but always trust the user from the session token
            if user_id and session_user_id != user_id:
                warning(f"User ID mismatch: Session belongs to {session_user_id} but request is from {user_id}")
            user_id = session_user_id  # Always use the session's user

            # 2. AUTO-START SESSION IF PENDING (UPDATE OPERATION)
            if session_details["session_status"] == "pending":
                info(f"Auto-starting pending session {decoded_session_code}")
                rows_updated = db_autostart_session_by_code(db, decoded_session_code)
                if rows_updated > 0:
                    db.commit()  # Commit the transaction
                    info(f"Successfully auto-started session {decoded_session_code}")
                    # Refresh session details to get new status and timestamps
                    session_details = get_session_and_assessment_details_by_code(decoded_session_code)
                else:
                    warning(f"Failed to auto-start session {decoded_session_code}, status may have changed.")

            # 3. GATHER ALL OTHER REQUIRED DATA (READ OPERATIONS)
            assessment_id = session_details["assessment_id"]
            assessment_details = db_get_assessment_details_by_id(db, assessment_id)
            is_fixed_assessment = assessment_details and assessment_details["question_selection_mode"] == "fixed"

            # Extract topic identifier from assessment name
            question_topic_identifier = (
                session_details["assessment_name"].replace(" Mock Assessment", "").replace(" Final Assessment", "")
            )

            all_questions_for_topic = _get_questions_for_assessment(
                db, assessment_id, is_fixed_assessment, question_topic_identifier, session_details["is_final"]
            )

            total_questions = _get_total_questions_count(db, assessment_id, is_fixed_assessment)

        # --- BUSINESS LOGIC: All database work is done. Now we process the data. ---

        # Determine which questions have already been attempted
        attempted_questions = []
        if session_details.get("attempted_questions") and not retake:
            attempted_questions = session_details.get("attempted_questions", [])
        else:
            attempted_questions = [] if retake else fetch_attempted_question_ids(question_topic_identifier, user_id)

        # Filter the questions based on various rules
        filtered_questions = _filter_questions_by_assessment_type(
            all_questions_for_topic,
            attempted_questions,
            is_fixed_assessment,
            session_details["is_final"],
            difficulty,
            retake,
            question_topic_identifier,
        )
        if not filtered_questions:
            error_msg = get_error_message(session_details["is_final"], question_topic_identifier, difficulty)
            return error_response(message=error_msg, code=status.HTTP_404_NOT_FOUND)

        # Determine how many questions to send back
        with get_db_context() as db:
            num_questions_to_return = _determine_questions_count(
                db, is_fixed_assessment, session_details["is_final"], filtered_questions, assessment_id
            )

            # Get skill IDs for dynamic assessments
            skill_ids = []
            if not is_fixed_assessment:
                skill_ids = db_get_skill_ids_for_assessment(db, assessment_id)

        # Select a final sample of questions
        selected_questions = _select_questions_for_response(
            filtered_questions, num_questions_to_return, is_fixed_assessment, skill_ids
        )
        if not selected_questions:
            error_msg = get_error_message(session_details["is_final"], question_topic_identifier, difficulty)
            raise_http_exception(status_code=404, detail=error_msg)

        # 4. FORMAT FINAL RESPONSE
        formatted_data = format_response(
            "final" if session_details["is_final"] else "mock",
            session_details["assessment_name"],
            assessment_details.get("description") if assessment_details else question_topic_identifier,
            selected_questions,
            remaining_time_seconds=session_details.get("remaining_time_seconds"),
            attempted_questions_count=len(attempted_questions),
            total_questions=total_questions,
        )
        return success_response(data=formatted_data, message="Questions retrieved successfully")

    except HTTPException:
        raise
    except Exception as e:
        error(f"Error fetching questions for session code {session_code}: {str(e)}", exc_info=True)
        raise_http_exception(status_code=500, detail="Failed to load questions.")


@quiz_router.get("/get_next_question/{session_code}")
def get_next_question(
    session_code: str,
    user_id: str = Query(...),
    difficulty: str = Query("easy"),
    retake: bool = Query(False),
):
    """
    Fetches the next quiz question for the user session.

    Args:
        session_code (str): The session code or hash.
        user_id (str): The external user ID of the quiz taker.
        difficulty (str, optional): The difficulty level. Defaults to "easy".
        retake (bool, optional): Whether to allow retakes. Defaults to False.

    Returns:
        dict: A dictionary containing the next question and session info.

    Raises:
        HTTPException
    """
    try:
        # Validate session
        decoded_session_code, session_details = validate_session_for_question(session_code)
        if not decoded_session_code:
            return session_details  # This is actually an error response

        # Validate user
        session_user_id = get_session_user_id(decoded_session_code)
        if session_user_id and session_user_id != user_id:
            warning(f"User ID mismatch: Session belongs to {session_user_id} but request is from {user_id}")
            user_id = session_user_id

        # Get assessment details
        assessment_id = session_details["assessment_id"]
        is_final = session_details["is_final"]
        question_topic_identifier = (
            session_details["assessment_name"].replace(" Mock Assessment", "").replace(" Final Assessment", "")
        )

        assessment_details = get_assessment_details(assessment_id)
        is_fixed_assessment = assessment_details and assessment_details["question_selection_mode"] == "fixed"

        # Debug logging to understand the issue
        info(f"Assessment {assessment_id} details: {assessment_details}")
        info(f"Is fixed assessment: {is_fixed_assessment}")

        # Get attempted questions and all available questions
        attempted_questions = []
        if not retake:
            # Get attempted questions from user_answers table for this session
            attempted_questions = list(fetch_attempted_question_ids_by_session(session_details["session_id"]))

        with get_db_context() as db:
            all_questions_for_topic = _get_questions_for_assessment(
                db, assessment_id, is_fixed_assessment, question_topic_identifier, is_final
            )

        # Debug logging to see how many questions are being returned
        info(f"Total questions returned for assessment {assessment_id}: {len(all_questions_for_topic)}")
        info(f"Attempted questions for session {session_details['session_id']}: {attempted_questions}")
        if is_fixed_assessment:
            info(f"Fixed assessment questions: {[q.get('que_id') for q in all_questions_for_topic]}")

        # Filter questions
        info(
            f"Before filtering - Total questions: {len(all_questions_for_topic)}, Attempted: {len(attempted_questions)}"
        )
        filtered_questions = _filter_questions(
            all_questions_for_topic, attempted_questions, difficulty, retake, is_fixed_assessment
        )
        info(f"After filtering - Filtered questions: {len(filtered_questions)}")

        # Check if questions are available
        if not filtered_questions:
            return error_response(
                message="No more questions available.",
                code=status.HTTP_404_NOT_FOUND,
                error_type="NotFound",
            )

        # Select next question randomly
        next_question = random.choice(filtered_questions)

        # Get additional data
        total_questions = _get_total_questions_count(None, assessment_id, is_fixed_assessment)
        current_score = _calculate_current_score(session_details["session_id"])

        # Format response
        response_data = _format_question_response(
            next_question, session_details, assessment_details, attempted_questions, total_questions, current_score
        )

        return success_response(data=response_data, message="Next question retrieved successfully")

    except HTTPException:
        raise
    except Exception as e:
        error(f"Error fetching next question for session code {session_code}: {str(e)}", exc_info=True)
        raise_http_exception(status_code=500, detail=f"Failed to load next question: {str(e)}")


def _filter_questions(
    all_questions: list, attempted_questions: list, difficulty: str, retake: bool, is_fixed_assessment: bool = False
) -> list:
    """Filter questions based on attempts, difficulty, and retake settings."""
    filtered_questions = []

    # Convert attempted questions to set of integers for consistent comparison
    attempted_set = set()
    for q_id in attempted_questions:
        try:
            attempted_set.add(int(q_id))
        except (ValueError, TypeError):
            warning(f"Invalid question ID in attempted_questions: {q_id}")

    for question in all_questions:
        if not question or "que_id" not in question:
            warning(f"Invalid question object: {question}")
            continue

        try:
            question_id = int(question["que_id"])
        except (ValueError, TypeError):
            warning(f"Invalid question ID: {question.get('que_id')}")
            continue

        # Skip attempted questions unless retake is allowed
        if question_id in attempted_set and not retake:
            continue

        # For fixed assessments, skip difficulty filtering - only filter by attempted questions
        if is_fixed_assessment:
            filtered_questions.append(question)
        else:
            # For dynamic assessments, apply difficulty filter
            if difficulty != "all" and question.get("level") != difficulty:
                continue
            filtered_questions.append(question)

    return filtered_questions
