from fastapi import APIRouter, Depends, status

from ...api.routes.assessment_routes import ReportRequest
from ...models.report_manager import (
    assessment_report_by_topic,
    assessment_report_by_user,
    assessment_report_with_question_stats,
    get_all_users,
    get_skillwise_heatmap_data,
)
from ...utils.api_response import error_response, success_response
from ...utils.logger import error
from ...utils.rate_limiter import rate_limiter

# Create router for report-related endpoints
report_router = APIRouter()

# =============================================================================
#  assessements reports
# =============================================================================


@report_router.post("/admin/reports")
async def generate_report(request: ReportRequest, _: None = Depends(rate_limiter)):
    """Generate reports based on user, topic, or assessment"""
    try:
        if request.report_type == "user_wise" and request.user_name:
            # Consistently handle the returned dictionary
            report_data = assessment_report_by_user(request.user_name, request.quiz_type or "mock")

            return success_response(
                data=report_data,
                message=f"Generated user-wise report for {request.user_name}",
            )

        elif request.report_type == "topic_wise" and request.report_topic:
            # Consistently handle the returned dictionary
            report_data = assessment_report_by_topic(request.report_topic, request.quiz_type or "mock")

            return success_response(
                data=report_data,
                message=f"Generated topic-wise report for {request.report_topic}",
            )

        elif request.report_type == "assessment_wise" and request.assessment_base_name:
            # This block is now consistent with the others
            report_data = assessment_report_with_question_stats(
                request.assessment_base_name, request.quiz_type or "mock"
            )

            return success_response(
                data=report_data,
                message=f"Generated assessment-wise report for {request.assessment_base_name}",
            )

        else:
            return error_response(
                message="Invalid report type or missing required parameters",
                code=status.HTTP_400_BAD_REQUEST,
                error_type="BadRequest",
            )

    except Exception as e:
        error(f"Error generating report: {str(e)}")
        return error_response(
            message=f"Error generating report: {str(e)}",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )


# =============================================================================
# skills reports
# =============================================================================


@report_router.get("/admin/reports/skillwise-heatmap")
async def get_skillwise_heatmap():
    """
    Get skill performance data for all users in a format suitable for a heatmap

    Returns a matrix of user-skill performance data with accuracy percentages
    """
    heatmap_data = get_skillwise_heatmap_data()

    if heatmap_data is None:
        return error_response(
            message="Error fetching skillwise heatmap data",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )

    return success_response(
        data=heatmap_data,
        message="Skillwise heatmap data retrieved successfully",
    )


# =============================================================================
# user management
# =============================================================================


@report_router.get("/admin/users")
async def get_users():
    """
    Get all users from the database for admin reporting

    Returns a list of all users with their basic information
    """
    try:
        users = get_all_users()
        return success_response(data={"users": users}, message=f"Retrieved {len(users)} users successfully")
    except Exception as e:
        error(f"Error fetching users: {str(e)}")
        return error_response(
            message=f"Error fetching users: {str(e)}",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )


# =============================================================================
# session reports
# =============================================================================
