import os
from typing import Optional

import requests
from authlib.integrations.httpx_client import <PERSON><PERSON><PERSON><PERSON>2<PERSON><PERSON>
from authlib.integrations.starlette_client import OAuth
from authlib.jose import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jwt
from dotenv import load_dotenv
from fastapi import APIRouter, HTTPException, Request
from fastapi.responses import RedirectResponse
from pydantic import BaseModel, ValidationError

from ...config.config import DEX_ISSUER, JWKS_URL, OAUTH_ERROR_MAP
from ...utils.logger import error as log_error
from ...utils.logger import log_auth_event
from ...utils.logger import warning as log_warning

# Load environment variables
load_dotenv()

router = APIRouter(prefix="/auth", tags=["Authentication"])


def validate_auth_config():
    """Validate that all required authentication environment variables are set."""
    required_vars = [
        "AUTH_CLIENT_ID",
        "AUTH_CLIENT_SECRET",
        "AUTH_TOKEN_URL",
        "AUTH_AUTHORIZE_URL",
        "AUTH_ISSUER",
        "AUTH_JWKS_URI",
        "AUTH_REDIRECT_URI",
        "FRONTEND_URL",
    ]

    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)

    if missing_vars:
        log_error(f"Missing required environment variables: {', '.join(missing_vars)}")
        raise RuntimeError(f"Missing required environment variables: {', '.join(missing_vars)}")


validate_auth_config()


class TokenRequest(BaseModel):
    code: str
    redirect_uri: str
    state: Optional[str] = None


oauth = OAuth()
oauth_config = {
    "name": "dex",
    "client_id": os.getenv("AUTH_CLIENT_ID"),
    "client_secret": os.getenv("AUTH_CLIENT_SECRET"),
    "access_token_url": os.getenv("AUTH_TOKEN_URL"),
    "authorize_url": os.getenv("AUTH_AUTHORIZE_URL"),
    "client_kwargs": {"scope": "openid email profile groups"},
}
oauth.register(**oauth_config)


def parse_dex_id_token(id_token: str) -> dict:
    try:
        jwks = JsonWebKey.import_key_set(requests.get(JWKS_URL).json())
        claims = jwt.decode(id_token, key=jwks)
        claims.validate()

        if claims.get("iss") != DEX_ISSUER:
            raise JoseError(f"Issuer mismatch: {claims.get('iss')} != {DEX_ISSUER}")

        log_auth_event("token_validation", success=True, user_id=claims.get("sub"))
        return dict(claims)
    except JoseError as e:
        log_auth_event("token_validation", success=False, error=str(e))
        raise HTTPException(status_code=401, detail="Invalid ID token")


@router.get("/login")
async def login(request: Request):
    redirect_uri = os.getenv("AUTH_REDIRECT_URI")
    return await oauth.dex.authorize_redirect(request, redirect_uri)


@router.get("/callback")
async def callback(request: Request):
    try:
        token = await oauth.dex.authorize_access_token(request)
        user = await oauth.dex.parse_id_token(request, token)

        if not user:
            log_auth_event("callback", success=False, error="No valid user information found")
            return RedirectResponse(url=f"{os.getenv('FRONTEND_URL')}/login?error=no_user_info")

        log_auth_event("callback", success=True, user_id=user.get("sub"))

        user_groups = user.get("groups", [])

        safe_user_info = {
            "sub": user.get("sub"),
            "name": user.get("name", "User"),
            "email": user.get("email", user.get("mail")),
            "groups": user_groups,
        }
        request.session["user"] = safe_user_info
        return RedirectResponse(url=f"{os.getenv('FRONTEND_URL')}/")
    except Exception as e:
        log_auth_event("callback", success=False, error=str(e))
        return RedirectResponse(url=f"{os.getenv('FRONTEND_URL')}/login?error=authentication_failed")


@router.get("/userinfo")
async def userinfo(request: Request):
    user = request.session.get("user")
    if user:
        safe_user_info = {
            "name": user.get("name", "User"),
            "email": user.get("email"),
            "groups": user.get("groups", []),
        }
        return {"authenticated": True, "user": safe_user_info}
    return {"authenticated": False, "user": None}


@router.get("/logout")
async def logout(request: Request):
    request.session.pop("user", None)

    accept_header = request.headers.get("accept", "")
    if "application/json" in accept_header:
        return {"success": True, "message": "Logged out successfully"}
    else:
        return RedirectResponse(url=os.getenv("AUTH_LOGOUT_URL"))


@router.post("/token")
async def exchange_token(request_data: TokenRequest, request: Request):
    """Exchange authorization code for access token."""
    try:
        # Validate required parameters
        if not request_data.code:
            raise HTTPException(status_code=400, detail="Missing required parameter: code")
        if not request_data.redirect_uri:
            raise HTTPException(status_code=400, detail="Missing required parameter: redirect_uri")

        token_url = os.getenv("AUTH_TOKEN_URL")
        if not token_url:
            raise HTTPException(status_code=500, detail="Authentication service not configured properly")

        expected_redirect_uri = os.getenv("AUTH_REDIRECT_URI")
        if expected_redirect_uri and request_data.redirect_uri != expected_redirect_uri:
            log_warning(f"Redirect URI mismatch: got {request_data.redirect_uri}, expected {expected_redirect_uri}")

        client = AsyncOAuth2Client(
            client_id=os.getenv("AUTH_CLIENT_ID"),
            client_secret=os.getenv("AUTH_CLIENT_SECRET"),
            token_endpoint=token_url,
        )

        token = await client.fetch_token(
            url=token_url,
            code=request_data.code,
            redirect_uri=request_data.redirect_uri,
        )

        id_token = token.get("id_token")
        user_info = {}

        if id_token:
            try:
                parsed_user_info = parse_dex_id_token(id_token)
                user_info = {
                    "sub": parsed_user_info.get("sub"),
                    "name": parsed_user_info.get("name", "User"),
                    "email": parsed_user_info.get("email") or parsed_user_info.get("mail"),
                    "groups": parsed_user_info.get("groups", ["employees"]),
                }
            except Exception as e:
                log_warning(f"Failed to parse ID token: {e}")

        if not user_info and (token.get("sub") or token.get("email") or token.get("name")):
            user_info = {
                "sub": token.get("sub", "user"),
                "name": token.get("name", "Authenticated User"),
                "email": token.get("email", token.get("mail")),
                "groups": token.get("groups", ["employees"]),
            }

        if not user_info:
            raise HTTPException(
                status_code=401,
                detail="Authentication failed: No valid user information found",
            )

        request.session["user"] = user_info
        return {
            "success": True,
            "message": "Authentication successful",
            "authenticated": True,
        }

    except ValidationError as ve:
        log_error(f"Validation error in token exchange: {ve}")
        raise HTTPException(status_code=400, detail=f"Invalid request format: {ve}")
    except Exception as e:
        log_error(f"Error exchanging token: {str(e)}", exception=e)
        error_str = str(e).lower()

        for key, (status_code, detail) in OAUTH_ERROR_MAP.items():
            if key in error_str:
                raise HTTPException(status_code=status_code, detail=detail)

        # Default fallback
        raise HTTPException(
            status_code=500,
            detail="An unexpected error occurred during authentication. Please try again or contact support.",
        )


def setup_auth(api_router):
    api_router.include_router(router)
