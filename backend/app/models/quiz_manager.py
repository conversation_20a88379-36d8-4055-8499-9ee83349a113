"""
This module provides quiz-related database operations using SQLAlchemy ORM.
"""

import os
from typing import Dict, List, Optional

from database.db import get_db_context
from database.models import (
    Assessment,
    AssessmentQuestion,
    AssessmentSkill,
    Question,
)
from database.models import Session as SessionModel
from database.models import (
    <PERSON><PERSON>,
    User,
    <PERSON><PERSON>A<PERSON><PERSON>,
    UserAssessment,
)
from dotenv import load_dotenv
from sqlalchemy import func, or_
from sqlalchemy.exc import SQLAlchemyError

from ..utils.logger import (
    error,
    log_database_error,
    log_database_operation,
    warning,
)
from .skill_manager import valid_skill_description

load_dotenv()

FINAL_QUESTION_COUNT = int(os.getenv("FINAL_QUESTION_COUNT", "20"))


def insert_question_data(data: list, skill_id: int = None):
    """
    Insert quiz question data into the `questions` table using SQLAlchemy ORM.
    Now accepts questions associated with any of the assessment's skills.

    Args:
        data (list): List of question data dictionaries to insert
        skill_id (int, optional): The skill ID to associate with these questions
    """
    try:
        with get_db_context() as db:
            for entry in data:
                # Validate topic matches any skill description for this assessment
                if not entry.get("Topic") or not valid_skill_description(entry["Topic"]):
                    raise ValueError("Topic must match a valid skill description")

                # Include skill_id in the insert if provided
                if skill_id is not None:
                    # Check if question already exists to avoid duplicates
                    existing_question = (
                        db.query(Question)
                        .filter(
                            Question.topic == entry["Topic"],
                            Question.level == entry["Level"],
                            Question.question == entry["Question"],
                            Question.skill_id == skill_id,
                        )
                        .first()
                    )

                    if not existing_question:
                        question = Question(
                            topic=entry["Topic"],
                            level=entry["Level"],
                            question=entry["Question"],
                            options=entry["Options"],
                            answer=entry["Answer"],
                            skill_id=skill_id,
                        )
                        db.add(question)
                else:
                    # Try to find the skill_id based on the topic
                    skill = (
                        db.query(Skill)
                        .filter(or_(Skill.name == entry["Topic"], Skill.name == entry["Topic"].split("_")[0]))
                        .first()
                    )

                    if skill:
                        found_skill_id = skill.id
                        # Check if question already exists to avoid duplicates
                        existing_question = (
                            db.query(Question)
                            .filter(
                                Question.topic == entry["Topic"],
                                Question.level == entry["Level"],
                                Question.question == entry["Question"],
                                Question.skill_id == found_skill_id,
                            )
                            .first()
                        )

                        if not existing_question:
                            question = Question(
                                topic=entry["Topic"],
                                level=entry["Level"],
                                question=entry["Question"],
                                options=entry["Options"],
                                answer=entry["Answer"],
                                skill_id=found_skill_id,
                            )
                            db.add(question)
                    else:
                        # If we can't find a skill_id, we can't insert the question
                        # due to the NOT NULL constraint
                        error(f"Cannot insert question without skill_id for topic: {entry['Topic']}")
                        raise ValueError(
                            f"No skill found for topic: {entry['Topic']}. Cannot insert question without skill_id."
                        )

            db.commit()
            log_database_operation("insert", "questions", affected_rows=len(data))
    except SQLAlchemyError as e:
        log_database_error("insert", "questions", e)
        raise
    except Exception as e:
        log_database_error("insert", "questions", e)
        raise


def insert_final_questions_db(question_ids):
    """
    Insert selected questions into a fixed assessment's question list only if there are
    enough questions in each category based on FINAL_QUESTION_COUNT.

    Args:
        question_ids (list): A list of question IDs to add to assessment.

    Returns:
        dict: A result message indicating success or failure.

    Note: This function is kept for backward compatibility. In the new schema,
    questions are associated with assessments through the assessment_questions table.
    """
    easy_count, intermediate_count, advanced_count = divide_number(FINAL_QUESTION_COUNT)

    try:
        with get_db_context() as db:
            # First check if all question IDs exist
            existing_questions = db.query(Question).filter(Question.que_id.in_(question_ids)).all()

            if len(existing_questions) != len(question_ids):
                return {"error": "Some question IDs do not exist"}

            # Count questions by level
            level_counts = {}
            for question in existing_questions:
                level = question.level
                level_counts[level] = level_counts.get(level, 0) + 1

            selected_easy = level_counts.get("easy", 0)
            selected_intermediate = level_counts.get("intermediate", 0)
            selected_advanced = level_counts.get("advanced", 0)

            if (
                selected_easy >= easy_count
                and selected_intermediate >= intermediate_count
                and selected_advanced >= advanced_count
            ):
                # Note: In the new schema, this would need to be associated with a specific assessment
                # This is a placeholder return for backward compatibility
                return {
                    "message": "Questions validated for final assessment.",
                    "question_ids": question_ids,
                    "note": "In the new schema, questions should be associated with a specific assessment",
                }

            return {
                "error": (
                    "Selection must contain at least "
                    f"{easy_count} easy, {intermediate_count} intermediate, "
                    f"and {advanced_count} advanced questions. Found: "
                    f"easy: {selected_easy}, intermediate: {selected_intermediate}, "
                    f"advanced: {selected_advanced}"
                )
            }
    except SQLAlchemyError as e:
        return {"error": f"Error validating final questions: {str(e)}"}


def fetch_questions(quiz_name):
    """
    Fetch all questions for a specific quiz from the `questions` table using SQLAlchemy ORM.

    Args:
        quiz_name (str): The name of the quiz to fetch questions for.

    Returns:
        List[Dict]: A list of dictionaries, each representing a question and its attributes.

    Notes:
        - The function fetches questions based on the `topic` (quiz_name).
        - Returns results as dictionaries for backward compatibility.
    """
    try:
        with get_db_context() as db:
            questions = db.query(Question).filter(Question.topic == quiz_name).all()

            # Convert to dict format for backward compatibility
            result = []
            for question in questions:
                question_dict = {
                    "que_id": question.que_id,
                    "topic": question.topic,
                    "level": question.level,
                    "question": question.question,
                    "options": question.options,
                    "answer": question.answer,
                    "time": question.time,
                    "topics": question.topics,
                    "skill_id": question.skill_id,
                }
                result.append(question_dict)

            return result
    except SQLAlchemyError as e:
        log_database_error("select", "questions", e, quiz_name=quiz_name)
        return []


def fetch_questions_for_skills(skill_ids, exclude_fixed_assessment_questions=True):
    """
    Fetch questions for specific skills from the `questions` table using SQLAlchemy ORM.

    Args:
        skill_ids (List[int]): List of skill IDs to fetch questions for.
        exclude_fixed_assessment_questions (bool): Whether to exclude questions
                                                  assigned to fixed assessments.

    Returns:
        List[Dict]: A list of dictionaries, each representing a question and its attributes.

    Notes:
        - For dynamic assessments, excludes questions assigned to fixed assessments by default.
        - Returns results as dictionaries for backward compatibility.
    """
    if not skill_ids:
        return []

    try:
        with get_db_context() as db:
            query = db.query(Question).filter(Question.skill_id.in_(skill_ids))

            if exclude_fixed_assessment_questions:
                # Exclude questions assigned to fixed assessments
                fixed_question_ids = (
                    db.query(AssessmentQuestion.question_id)
                    .join(Assessment, AssessmentQuestion.assessment_id == Assessment.id)
                    .filter(Assessment.question_selection_mode == "fixed")
                    .subquery()
                )

                query = query.filter(~Question.que_id.in_(fixed_question_ids))

            questions = query.all()

            # Convert to dict format for backward compatibility
            result = []
            for question in questions:
                question_dict = {
                    "que_id": question.que_id,
                    "topic": question.topic,
                    "level": question.level,
                    "question": question.question,
                    "options": question.options,
                    "answer": question.answer,
                    "time": question.time,
                    "topics": question.topics,
                    "skill_id": question.skill_id,
                }
                result.append(question_dict)

            return result
    except SQLAlchemyError as e:
        log_database_error("select", "questions", e, skill_ids=skill_ids)
        return []


def count_questions_for_skills(skill_ids, exclude_fixed_assessment_questions=True):
    """
    Count questions available for specific skills using SQLAlchemy ORM.

    Args:
        skill_ids (List[int]): List of skill IDs to count questions for.
        exclude_fixed_assessment_questions (bool): Whether to exclude questions
                                                  assigned to fixed assessments.

    Returns:
        int: The count of available questions.

    Notes:
        - For dynamic assessments, excludes questions assigned to fixed assessments by default.
    """
    if not skill_ids:
        return 0

    try:
        with get_db_context() as db:
            query = db.query(Question).filter(Question.skill_id.in_(skill_ids))

            if exclude_fixed_assessment_questions:
                # Exclude questions assigned to fixed assessments
                fixed_question_ids = (
                    db.query(AssessmentQuestion.question_id)
                    .join(Assessment, AssessmentQuestion.assessment_id == Assessment.id)
                    .filter(Assessment.question_selection_mode == "fixed")
                    .subquery()
                )

                query = query.filter(~Question.que_id.in_(fixed_question_ids))

            return query.count()
    except SQLAlchemyError as e:
        log_database_error("count", "questions", e, skill_ids=skill_ids)
        return 0


def fetch_final_questions(quiz_name):
    """
    Fetch all questions that are part of fixed assessments for a specific quiz using SQLAlchemy ORM.

    This function has been updated to work with the current schema where questions
    are associated with assessments through the assessment_questions table.

    Args:
        quiz_name (str): The name of the quiz to fetch questions for.

    Returns:
        List[Dict]: A list of dictionaries, each representing a question and its attributes.
    """
    try:
        with get_db_context() as db:
            # Get questions that are part of fixed assessments related to this topic
            questions = (
                db.query(Question)
                .join(AssessmentQuestion, Question.que_id == AssessmentQuestion.question_id)
                .join(Assessment, AssessmentQuestion.assessment_id == Assessment.id)
                .filter(Assessment.question_selection_mode == "fixed", Assessment.name.like(f"%{quiz_name}%"))
                .all()
            )

            # Convert to dict format for backward compatibility
            result = []
            for question in questions:
                question_dict = {
                    "que_id": question.que_id,
                    "topic": question.topic,
                    "level": question.level,
                    "question": question.question,
                    "options": question.options,
                    "answer": question.answer,
                    "time": question.time,
                    "topics": question.topics,
                    "skill_id": question.skill_id,
                }
                result.append(question_dict)

            return result
    except SQLAlchemyError as e:
        error(f"Error fetching fixed assessment questions: {e}")
        return []


def fetch_attempted_question_ids(quiz_name, user_id):
    """
    Fetch the IDs of questions that have already been attempted by a specific user using SQLAlchemy ORM.

    Args:
        quiz_name (str): The name of the quiz (topic) to filter the attempted questions.
        user_id (str): The unique identifier of
        the user whose attempted question IDs need to be fetched.

    Returns:
        Set[int]: A set of question IDs that have been attempted by the user in the given quiz.

    Notes:
        - The returned set ensures that each question ID is unique,
          as sets inherently avoid duplicates.
        - This function queries the `user_assessment` table
          to retrieve the question IDs (`que_id`) for the specified user and quiz.
    """
    try:
        with get_db_context() as db:
            user_assessments = (
                db.query(UserAssessment.que_id)
                .filter(UserAssessment.topic == quiz_name, UserAssessment.user_id == user_id)
                .all()
            )

            return {assessment.que_id for assessment in user_assessments}
    except SQLAlchemyError as e:
        log_database_error("select", "user_answers", e, quiz_name=quiz_name, user_id=user_id)
        return set()


def fetch_attempted_question_ids_by_session(session_id):
    """
    Fetch the IDs of questions that have already been attempted in a specific session using SQLAlchemy ORM.

    Args:
        session_id (int): The internal session ID to filter the attempted questions.

    Returns:
        Set[int]: A set of question IDs that have been attempted in the given session.

    Notes:
        - This function queries the `user_answers` table to get actual attempted questions
          for the session-based assessments.
        - The returned set ensures that each question ID is unique.
    """
    try:
        with get_db_context() as db:
            user_answers = db.query(UserAnswer.question_id).filter(UserAnswer.session_id == session_id).all()

            return {answer.question_id for answer in user_answers}
    except SQLAlchemyError as e:
        log_database_error("select", "user_answers", e, session_id=session_id)
        return set()


def get_questions_by_level(quiz_name: str, level: str):
    """
    Retrieve questions from the `questions` table filtered by a specific level using SQLAlchemy ORM.

    Args:
        quiz_name (str): The name of the quiz (topic) for which to retrieve questions.
        level (str): The level of the questions
        to retrieve (e.g., "easy", "intermediate", "advanced").

    Returns:
        list: A list of questions (strings) for the specified level and topic.

    Raises:
        ValueError: If `level` is not a string.

    Notes:
        - This function queries the `questions` table,
          retrieving only the questions matching the given `quiz_name` and `level`.
        - If no questions are found or an error occurs, an empty list is returned.
    """
    if not isinstance(level, str):
        raise ValueError("`level` must be a string.")
    try:
        with get_db_context() as db:
            questions = db.query(Question.question).filter(Question.topic == quiz_name, Question.level == level).all()

            return [question.question for question in questions]
    except SQLAlchemyError as e:
        log_database_error("select", "questions", e, quiz_name=quiz_name, level=level)
        return []


def get_final_question_ids(quiz_name: str):
    """
    Retrieve the question IDs for questions that are part of fixed assessments using SQLAlchemy ORM.

    This function has been updated to work with the current schema where questions
    are associated with assessments through the assessment_questions table.

    Args:
        quiz_name (str): The name of the quiz (topic) for which to retrieve question IDs.

    Returns:
        list: A list of question IDs for questions in fixed assessments.

    Raises:
        ValueError: If `quiz_name` is not a string.
    """
    if not isinstance(quiz_name, str):
        raise ValueError("`quiz_name` must be a string.")
    try:
        with get_db_context() as db:
            # Get questions that are part of fixed assessments related to this topic
            question_ids = (
                db.query(AssessmentQuestion.question_id)
                .join(Assessment, AssessmentQuestion.assessment_id == Assessment.id)
                .filter(Assessment.question_selection_mode == "fixed", Assessment.name.like(f"%{quiz_name}%"))
                .distinct()
                .all()
            )

            return [question_id.question_id for question_id in question_ids]
    except SQLAlchemyError as e:
        error(f"Error retrieving fixed assessment question IDs: {e}")
        return []


def get_questions_for_check(quiz_name: str, question_id: str):
    """
    Retrieve a specific question from the `questions` table using SQLAlchemy ORM.

    Args:
        quiz_name (str): The name of the quiz (topic) to which the question belongs.
        question_id (str): The ID of the question to retrieve.

    Returns:
        dict: A dictionary containing the question data,
        including ID, text, options, answer, level, and topic.
        None: If no question is found or an error occurs.

    Raises:
        ValueError: If `question_id` cannot be converted to an integer.

    Notes:
        - The `question_id` should be a valid integer.
        - If the specified question doesn't exist, `None` will be returned.
    """
    try:
        question_id = int(question_id)

        with get_db_context() as db:
            question = db.query(Question).filter(Question.topic == quiz_name, Question.que_id == question_id).first()

            if question:
                return {
                    "que_id": question.que_id,
                    "question": question.question,
                    "options": question.options,
                    "answer": question.answer,
                    "level": question.level,
                    "topic": question.topic,
                }
            return None
    except ValueError:
        warning(f"Invalid question_id value: {question_id}")
        return None
    except SQLAlchemyError as e:
        log_database_error("select", "questions", e, question_id=question_id)
        return None


def db_upsert_user_answer(
    db, session_id: int, question_id: int, answer: str, is_correct: bool, score: float, time_taken: Optional[int]
):
    """
    Inserts a new user answer or updates it if it already exists (upsert) using SQLAlchemy ORM.
    This function only executes the query; it does not commit the transaction.
    """
    # Check if the user answer already exists
    existing_answer = (
        db.query(UserAnswer).filter(UserAnswer.session_id == session_id, UserAnswer.question_id == question_id).first()
    )

    if existing_answer:
        # Update existing answer
        existing_answer.user_answer = answer
        existing_answer.is_correct = is_correct
        existing_answer.score = score
        existing_answer.time_taken = time_taken
    else:
        # Insert new answer
        user_answer = UserAnswer(
            session_id=session_id,
            question_id=question_id,
            user_answer=answer,
            is_correct=is_correct,
            score=score,
            time_taken=time_taken,
        )
        db.add(user_answer)


def db_get_question_by_id(db, question_id: int) -> Optional[Dict]:
    """
    Fetches a single question from the database by its primary key (que_id) using SQLAlchemy ORM.

    Returns:
        A dictionary representing the question, or None if not found.
    """
    question = db.query(Question).filter(Question.que_id == question_id).first()

    if question:
        return {
            "que_id": question.que_id,
            "question": question.question,
            "options": question.options,
            "answer": question.answer,
            "level": question.level,
            "topic": question.topic,
        }
    return None


def db_get_assessment_total_questions(db, assessment_id: int) -> Optional[int]:
    """
    Fetches the 'total_questions' value for a specific assessment using SQLAlchemy ORM.

    Returns:
        The integer value of total_questions, or None if not found.
    """
    assessment = db.query(Assessment.total_questions).filter(Assessment.id == assessment_id).first()
    return assessment.total_questions if assessment else None


def db_get_skill_ids_for_assessment(db, assessment_id: int) -> List[int]:
    """
    Fetches a list of skill IDs associated with a dynamic assessment using SQLAlchemy ORM.

    Returns:
        A list of integer skill IDs.
    """
    assessment_skills = db.query(AssessmentSkill.skill_id).filter(AssessmentSkill.assessment_id == assessment_id).all()
    return [skill.skill_id for skill in assessment_skills]


def db_count_questions_in_fixed_assessment(db, assessment_id: int) -> int:
    """
    Counts the number of questions linked directly to a fixed assessment using SQLAlchemy ORM.

    Returns:
        The integer count of questions.
    """
    return db.query(AssessmentQuestion).filter(AssessmentQuestion.assessment_id == assessment_id).count()


def db_get_sum_of_scores_for_session(db, session_id: int) -> Optional[float]:
    """
    Calculates the sum of scores for all answers in a given session using SQLAlchemy ORM.
    The SUM function in SQL will return NULL if there are no rows.

    Returns:
        The total score as a float, or None if no answers exist for the session.
    """
    total_score = db.query(func.sum(UserAnswer.score)).filter(UserAnswer.session_id == session_id).scalar()

    return float(total_score) if total_score is not None else None


def db_get_user_external_id_by_internal_id(db, user_id: int) -> Optional[str]:
    """
    Fetches a user's external_id using their internal integer ID using SQLAlchemy ORM.

    Returns:
        The user's external_id string, or None if not found.
    """
    user = db.query(User.external_id).filter(User.id == user_id).first()
    return user.external_id if user else None


def db_autostart_session_by_code(db, session_code: str) -> int:
    """
    Updates a 'pending' session to 'in_progress' and sets the start time using SQLAlchemy ORM.
    This is an atomic operation designed for auto-starting sessions.

    Returns:
        The number of rows affected (0 or 1).
    """
    from datetime import datetime

    updated_rows = (
        db.query(SessionModel)
        .filter(SessionModel.code == session_code, SessionModel.status == "pending")
        .update({SessionModel.status: "in_progress", SessionModel.started_at: datetime.utcnow()})
    )

    return updated_rows


def db_get_assessment_details_by_id(db, assessment_id: int) -> Optional[Dict]:
    """
    Fetches key details for a single assessment by its ID using SQLAlchemy ORM.

    Returns:
        A dictionary of assessment details, or None if not found.
    """
    assessment = db.query(Assessment).filter(Assessment.id == assessment_id).first()

    if assessment:
        return {
            "id": assessment.id,
            "question_selection_mode": assessment.question_selection_mode,
            "name": assessment.name,
            "description": assessment.description,
            "is_final": assessment.is_final,
        }
    return None


def divide_number(n):
    """
    Get question counts for each difficulty level from environment variables.
    This function no longer divides the number, but uses environment variables instead.
    The parameter n is kept for backward compatibility.
    """
    easy_count = int(os.getenv("EASY_QUESTIONS_COUNT", "10"))
    intermediate_count = int(os.getenv("INTERMEDIATE_QUESTIONS_COUNT", "10"))
    advanced_count = int(os.getenv("ADVANCED_QUESTIONS_COUNT", "10"))

    return easy_count, intermediate_count, advanced_count
