from app.models.db_manager import calculate_percentage, calculate_total_score
from app.utils.logger import error, info, log_database_error, warning
from app.utils.performance_utils import get_performance_level
from database.db import get_db_context
from database.models import Question, Session, Skill, User, <PERSON>r<PERSON><PERSON><PERSON>, UserAssessment
from sqlalchemy import case, distinct, func

# =============================================================================
# assessments
# =============================================================================


def assessment_report_by_user(user_name: str):
    """
    Retrieve user assessment data and generate a detailed report including
    both individual assessments and aggregated score summaries.

    Args:
        user_name (str): The username to filter the assessment data.

    Returns:
        dict: A dictionary containing:
            - 'base_report': A list of dictionaries with detailed user assessments.
            - 'score_report': A list of dictionaries with aggregated user scores.

    Raises:
        Exception: If any error occurs while fetching data from the database.

    Notes:
        - The function retrieves both individual question-level details
        and aggregated performance metrics.
        - The 'base_report' provides a breakdown of each attempted question.
        - The 'score_report' summarizes scores, attempts, and performance levels
        per quiz topic and date.
        - The `performance_level` is determined based on the user's obtained score.
    """

    try:
        with get_db_context() as db:
            # Query for base report (detailed assessments)
            base_rows = (
                db.query(UserAssessment)
                .filter(UserAssessment.user_id == user_name)
                .order_by(UserAssessment.topic.asc(), UserAssessment.time.asc())
                .all()
            )

            base_report = (
                [
                    {
                        "id": row.id,
                        "user_id": row.user_id,
                        "topic": row.topic,
                        "level": row.level,
                        "quiz_type": row.quiz_type,
                        "que_id": row.que_id,
                        "question": row.question,
                        "options": row.options,
                        "user_answer": row.user_answer,
                        "correct_answer": row.correct_answer,
                        "result": row.result,
                        "score": row.score,
                        "time": row.time,
                    }
                    for row in base_rows
                ]
                if base_rows
                else []
            )

            # Query for score report (aggregated scores)
            score_query = (
                db.query(
                    UserAssessment.user_id,
                    UserAssessment.topic,
                    UserAssessment.quiz_type,
                    func.sum(UserAssessment.score).label("obtained_score"),
                    func.sum(case((UserAssessment.level == "easy", 1), else_=0)).label("easy_attempted"),
                    func.sum(
                        case(((UserAssessment.result == "Correct") & (UserAssessment.level == "easy"), 1), else_=0)
                    ).label("easy_correct"),
                    func.sum(case((UserAssessment.level == "intermediate", 1), else_=0)).label(
                        "intermediate_attempted"
                    ),
                    func.sum(
                        case(
                            ((UserAssessment.result == "Correct") & (UserAssessment.level == "intermediate"), 1),
                            else_=0,
                        )
                    ).label("intermediate_correct"),
                    func.sum(case((UserAssessment.level == "advanced", 1), else_=0)).label("advanced_attempted"),
                    func.sum(
                        case(((UserAssessment.result == "Correct") & (UserAssessment.level == "advanced"), 1), else_=0)
                    ).label("advanced_correct"),
                    func.date(UserAssessment.time).label("assessment_date"),
                )
                .filter(UserAssessment.user_id == user_name)
                .group_by(
                    UserAssessment.user_id,
                    UserAssessment.topic,
                    func.date(UserAssessment.time),
                    UserAssessment.quiz_type,
                )
                .order_by(UserAssessment.quiz_type)
                .all()
            )

            score_report = []
            for row in score_query:
                total_score = calculate_total_score(
                    row.easy_attempted, row.intermediate_attempted, row.advanced_attempted
                )

                score_report.append(
                    {
                        "user_id": row.user_id,
                        "topic": row.topic,
                        "total_score": total_score,
                        "obtained_score": row.obtained_score,
                        "quiz_type": row.quiz_type,
                        "percentage": calculate_percentage(row.obtained_score, total_score, row.quiz_type),
                        "performance_level": get_performance_level(row.obtained_score, total_score),
                        "easy_attempted": row.easy_attempted,
                        "easy_correct": row.easy_correct,
                        "intermediate_attempted": row.intermediate_attempted,
                        "intermediate_correct": row.intermediate_correct,
                        "advanced_attempted": row.advanced_attempted,
                        "advanced_correct": row.advanced_correct,
                        "assessment_date": row.assessment_date,
                    }
                )

            return {"base_report": base_report, "score_report": score_report}

    except Exception as e:
        log_database_error("select", "user_assessment", e, user_name=user_name)
        return {"base_report": [], "score_report": []}


def assessment_report_by_topic(topic: str, quiz_type: str):
    """
    Retrieve user assessment data and optionally include score reports
    based on topic and quiz type.

    Args:
        topic (str): The topic to filter the assessment data.
        quiz_type (str): The type of quiz to filter the assessment data.

    Returns:
        dict: A dictionary containing 'base_report' (detailed assessments)
              and 'score_report' (aggregated user scores).

    Raises:
        ValueError: If the `quiz_type` is not valid.

    Notes:
        - The `topic` should match an existing topic in the database.
        - The `quiz_type` must match a valid quiz type ('mock' or 'final').
    """
    try:
        with get_db_context() as db:
            # Query for base report (detailed assessments)
            base_rows = (
                db.query(UserAssessment)
                .filter(UserAssessment.topic == topic, UserAssessment.quiz_type == quiz_type)
                .order_by(UserAssessment.user_id.asc(), UserAssessment.time.asc())
                .all()
            )

            base_report = (
                [
                    {
                        "id": row.id,
                        "user_id": row.user_id,
                        "topic": row.topic,
                        "level": row.level,
                        "quiz_type": row.quiz_type,
                        "que_id": row.que_id,
                        "question": row.question,
                        "options": row.options,
                        "user_answer": row.user_answer,
                        "correct_answer": row.correct_answer,
                        "result": row.result,
                        "score": row.score,
                        "time": row.time,
                    }
                    for row in base_rows
                ]
                if base_rows
                else []
            )

            # Query for score report (aggregated scores)
            score_query = (
                db.query(
                    UserAssessment.user_id,
                    UserAssessment.topic,
                    func.sum(UserAssessment.score).label("obtained_score"),
                    func.sum(case((UserAssessment.level == "easy", 1), else_=0)).label("easy_attempted"),
                    func.sum(
                        case(((UserAssessment.result == "Correct") & (UserAssessment.level == "easy"), 1), else_=0)
                    ).label("easy_correct"),
                    func.sum(case((UserAssessment.level == "intermediate", 1), else_=0)).label(
                        "intermediate_attempted"
                    ),
                    func.sum(
                        case(
                            ((UserAssessment.result == "Correct") & (UserAssessment.level == "intermediate"), 1),
                            else_=0,
                        )
                    ).label("intermediate_correct"),
                    func.sum(case((UserAssessment.level == "advanced", 1), else_=0)).label("advanced_attempted"),
                    func.sum(
                        case(((UserAssessment.result == "Correct") & (UserAssessment.level == "advanced"), 1), else_=0)
                    ).label("advanced_correct"),
                )
                .filter(UserAssessment.topic == topic, UserAssessment.quiz_type == quiz_type)
                .group_by(UserAssessment.user_id, UserAssessment.topic)
                .order_by(UserAssessment.topic)
                .all()
            )

            score_report = []
            for row in score_query:
                total_score = calculate_total_score(
                    row.easy_attempted, row.intermediate_attempted, row.advanced_attempted
                )

                score_report.append(
                    {
                        "user_id": row.user_id,
                        "topic": row.topic,
                        "total_score": total_score,
                        "obtained_score": row.obtained_score,
                        "percentage": calculate_percentage(row.obtained_score, total_score, quiz_type),
                        "performance_level": get_performance_level(row.obtained_score, total_score),
                        "easy_attempted": row.easy_attempted,
                        "easy_correct": row.easy_correct,
                        "intermediate_attempted": row.intermediate_attempted,
                        "intermediate_correct": row.intermediate_correct,
                        "advanced_attempted": row.advanced_attempted,
                        "advanced_correct": row.advanced_correct,
                    }
                )

            return {"base_report": base_report, "score_report": score_report}

    except Exception as e:
        log_database_error("select", "user_assessment", e, topic=topic, quiz_type=quiz_type)
        return {"base_report": [], "score_report": []}


def assessment_report_with_question_stats(assessment_base_name: str, quiz_type: str):
    """
    Retrieve assessment data with question-wise statistics including user attendance.

    Args:
        assessment_base_name (str): The base name of the assessment (e.g., "data engineer_22_05_2025").
        quiz_type (str): The type of quiz to filter the assessment data (not used in new structure).

    Returns:
        dict: A dictionary containing 'base_report' (question-wise stats)
              and 'score_report' (aggregated user scores).
    """
    try:
        from database.db import get_db_context
        from database.models import Assessment, Question, Session, Skill, User, UserAnswer
        from sqlalchemy import case, distinct, func

        with get_db_context() as db:
            # Find assessments matching the base name pattern
            assessments = (
                db.query(Assessment.id, Assessment.name, Assessment.question_selection_mode)
                .filter(Assessment.name.like(f"{assessment_base_name}%"))
                .all()
            )

            if not assessments:
                warning(f"No assessments found for pattern starting with '{assessment_base_name}'. Exiting.")
                return {"base_report": [], "score_report": []}

            # Use the first matching assessment
            assessment_id = assessments[0][0]
            assessment_name = assessments[0][1]

            # Query for question statistics
            question_stats = (
                db.query(
                    Question.que_id,
                    Question.skill_id,
                    Skill.name.label("skill_name"),
                    Question.level,
                    Question.question,
                    Question.options,
                    Question.answer,
                    Question.topic.label("question_topic"),
                    func.count(distinct(Session.user_id)).label("total_attended_users"),
                    func.count(case((UserAnswer.is_correct, 1))).label("correct_answers"),
                    func.count(UserAnswer.session_id).label("total_attempts"),
                    func.round(
                        func.count(case((UserAnswer.is_correct, 1)))
                        * 100.0
                        / func.nullif(func.count(UserAnswer.session_id), 0),
                        2,
                    ).label("correct_percentage"),
                )
                .outerjoin(Skill, Question.skill_id == Skill.id)
                .join(UserAnswer, Question.que_id == UserAnswer.question_id)
                .join(Session, UserAnswer.session_id == Session.id)
                .filter(Session.assessment_id == assessment_id)
                .group_by(
                    Question.que_id,
                    Question.skill_id,
                    Skill.name,
                    Question.level,
                    Question.question,
                    Question.options,
                    Question.answer,
                    Question.topic,
                )
                .order_by(Skill.name, Question.level, Question.question)
                .all()
            )

            base_report = []
            if question_stats:
                for row in question_stats:
                    base_report.append(
                        {
                            "que_id": row.que_id,
                            "skill_name": row.skill_name or "Unknown",
                            "level": row.level,
                            "question": row.question,
                            "options": row.options,
                            "correct_answer": row.answer,
                            "question_topic": row.question_topic,
                            "total_attended_users": row.total_attended_users,
                            "correct_answers": row.correct_answers,
                            "total_attempts": row.total_attempts,
                            "correct_percentage": row.correct_percentage or 0.0,
                        }
                    )

            # Query for aggregated score report
            score_stats = (
                db.query(
                    User.external_id.label("user_id"),
                    func.sum(UserAnswer.score).label("obtained_score"),
                    func.sum(case((Question.level == "easy", 1), else_=0)).label("easy_attempted"),
                    func.sum(case(((UserAnswer.is_correct) & (Question.level == "easy"), 1), else_=0)).label(
                        "easy_correct"
                    ),
                    func.sum(case((Question.level == "intermediate", 1), else_=0)).label("intermediate_attempted"),
                    func.sum(case(((UserAnswer.is_correct) & (Question.level == "intermediate"), 1), else_=0)).label(
                        "intermediate_correct"
                    ),
                    func.sum(case((Question.level == "advanced", 1), else_=0)).label("advanced_attempted"),
                    func.sum(case(((UserAnswer.is_correct) & (Question.level == "advanced"), 1), else_=0)).label(
                        "advanced_correct"
                    ),
                )
                .join(Session, UserAnswer.session_id == Session.id)
                .join(User, Session.user_id == User.id)
                .join(Question, UserAnswer.question_id == Question.que_id)
                .filter(Session.assessment_id == assessment_id)
                .group_by(User.external_id)
                .order_by(User.external_id)
                .all()
            )

            score_report = []
            if score_stats:
                for row in score_stats:
                    total_score = calculate_total_score(
                        row.easy_attempted, row.intermediate_attempted, row.advanced_attempted
                    )
                    score_report.append(
                        {
                            "user_id": row.user_id,
                            "topic": assessment_name,
                            "total_score": total_score,
                            "obtained_score": row.obtained_score,
                            "percentage": calculate_percentage(row.obtained_score, total_score, quiz_type),
                            "performance_level": get_performance_level(row.obtained_score, total_score),
                            "easy_attempted": row.easy_attempted,
                            "easy_correct": row.easy_correct,
                            "intermediate_attempted": row.intermediate_attempted,
                            "intermediate_correct": row.intermediate_correct,
                            "advanced_attempted": row.advanced_attempted,
                            "advanced_correct": row.advanced_correct,
                        }
                    )

            info("Successfully built base_report and score_report. Returning data.")
            return {"base_report": base_report, "score_report": score_report}

    except Exception as e:
        log_database_error("select", "user_assessment", e, assessment_base_name=assessment_base_name)
        return {"base_report": [], "score_report": []}


# =============================================================================
# skills
# =============================================================================


def get_skillwise_heatmap_data():
    """
    Get skill performance data for all users in a format suitable for a heatmap.

    Returns:
        dict: A dictionary containing users, skills, and performance data for the heatmap
              or None if an error occurs.
    """
    try:
        with get_db_context() as db:
            # Get all users, not just those who have answered questions
            users_query = db.query(User.id, User.display_name, User.external_id).order_by(User.display_name).all()
            users = [
                {"id": user.id, "display_name": user.display_name, "external_id": user.external_id}
                for user in users_query
            ]

            # Get all skills
            skills_query = db.query(Skill.id, Skill.name).order_by(Skill.name).all()
            skills = [{"id": skill.id, "name": skill.name} for skill in skills_query]

            # Get performance data for all users and skills
            performance_query = (
                db.query(
                    User.id.label("user_id"),
                    User.display_name,
                    User.external_id,
                    Skill.id.label("skill_id"),
                    Skill.name.label("skill_name"),
                    func.count(UserAnswer.id).label("total_questions_answered"),
                    func.sum(case((UserAnswer.is_correct, 1), else_=0)).label("correct_answers"),
                    func.round(
                        (func.sum(case((UserAnswer.is_correct, 1), else_=0)) * 100.0)
                        / func.nullif(func.count(UserAnswer.id), 0),
                        0,
                    ).label("accuracy_percentage"),
                )
                .join(Session, User.id == Session.user_id)
                .join(UserAnswer, Session.id == UserAnswer.session_id)
                .join(Question, UserAnswer.question_id == Question.que_id)
                .join(Skill, Question.skill_id == Skill.id)
                .group_by(User.id, User.display_name, User.external_id, Skill.id, Skill.name)
                .order_by(User.display_name, Skill.name)
                .all()
            )

            performance_data = []
            for row in performance_query:
                performance_data.append(
                    {
                        "user_id": row.user_id,
                        "display_name": row.display_name,
                        "external_id": row.external_id,
                        "skill_id": row.skill_id,
                        "skill_name": row.skill_name,
                        "total_questions_answered": int(row.total_questions_answered),
                        "correct_answers": int(row.correct_answers),
                        "accuracy_percentage": (
                            float(row.accuracy_percentage) if row.accuracy_percentage is not None else 0
                        ),
                    }
                )

            return {
                "users": users,
                "skills": skills,
                "performance_data": performance_data,
            }

    except Exception as e:
        error(f"Error fetching skillwise heatmap data: {str(e)}")
        return None


# =============================================================================
# users
# =============================================================================


def get_all_users():
    """
    Get all users from the database with their assessment and session counts.

    Returns:
        list: A list of dictionaries containing user information including:
            - id: User internal ID
            - external_id: User external ID
            - email: User email
            - name: User display name or email
            - display_name: User display name
            - created_at: User creation timestamp
            - assessments: Number of assessments taken
            - session_count: Number of sessions

    Raises:
        Exception: If any error occurs while fetching data from the database.
    """
    try:
        with get_db_context() as db:
            # Query to get all users with assessment and session counts
            result = (
                db.query(
                    User.id,
                    User.external_id,
                    User.email,
                    User.display_name,
                    User.created_at,
                    func.count(distinct(UserAssessment.id)).label("assessment_count"),
                    func.count(distinct(Session.id)).label("session_count"),
                )
                .outerjoin(UserAssessment, User.external_id == UserAssessment.user_id)
                .outerjoin(Session, User.id == Session.user_id)
                .group_by(User.id, User.external_id, User.email, User.display_name, User.created_at)
                .order_by(User.created_at.desc())
                .all()
            )

            users = []
            for row in result:
                users.append(
                    {
                        "id": row.id,
                        "external_id": row.external_id,
                        "email": row.email,
                        # Use display_name or email as name
                        "name": row.display_name or row.email,
                        "display_name": row.display_name or row.email,
                        "created_at": row.created_at.isoformat() if row.created_at else None,
                        "assessments": row.assessment_count,
                        "session_count": row.session_count,
                    }
                )

            return users

    except Exception as e:
        error(f"Error fetching all users: {str(e)}")
        raise
