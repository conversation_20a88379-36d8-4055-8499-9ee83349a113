from typing import Dict, List, Optional

from database.models import AssessmentSkill, Question, Session, Skill, User, UserAnswer
from sqlalchemy import case, func

from ..utils.db_utils import with_db


@with_db
def db_get_skill_performance(db, user_external_id: str, assessment_id: Optional[int] = None) -> List[Dict]:
    """
    Fetches aggregated performance data grouped by skill for a specific user.
    """
    query = (
        db.query(
            Skill.name.label("skill_name"),
            func.count(UserAnswer.id).label("total_questions"),
            func.sum(case((UserAnswer.is_correct, 1), else_=0)).label("correct_answers"),
            (func.avg(case((UserAnswer.is_correct, 1.0), else_=0.0)) * 100).label("accuracy_percentage"),
            func.sum(UserAnswer.score).label("total_score"),
        )
        .join(AssessmentSkill, AssessmentSkill.skill_id == Skill.id)
        .join(Session, Session.assessment_id == AssessmentSkill.assessment_id)
        .join(User, Session.user_id == User.id)
        .join(User<PERSON>nswer, UserAnswer.session_id == Session.id)
        .filter(User.external_id == user_external_id)
    )

    if assessment_id:
        query = query.filter(Session.assessment_id == assessment_id)

    result = query.group_by(Skill.id, Skill.name).order_by(Skill.name).all()

    return [
        {
            "skill_name": row.skill_name,
            "total_questions": row.total_questions,
            "correct_answers": row.correct_answers,
            "accuracy_percentage": row.accuracy_percentage,
            "total_score": row.total_score,
        }
        for row in result
    ]


@with_db
def db_get_overall_performance(db, user_external_id: str, assessment_id: Optional[int] = None) -> Optional[Dict]:
    """
    Fetches an overall performance summary for a user.
    """
    query = (
        db.query(
            func.count(UserAnswer.id).label("total_questions"),
            func.sum(case((UserAnswer.is_correct, 1), else_=0)).label("correct_answers"),
            func.sum(UserAnswer.score).label("total_score"),
            func.avg(UserAnswer.time_taken).label("average_time_taken"),
        )
        .join(Session, UserAnswer.session_id == Session.id)
        .join(User, Session.user_id == User.id)
        .filter(User.external_id == user_external_id)
    )

    if assessment_id:
        query = query.filter(Session.assessment_id == assessment_id)

    result = query.first()

    if result and result.total_questions > 0:
        return {
            "total_questions": result.total_questions,
            "correct_answers": result.correct_answers,
            "total_score": result.total_score,
            "average_time_taken": result.average_time_taken,
        }
    return None


@with_db
def db_get_difficulty_wise_performance(db, user_external_id: str, assessment_id: Optional[int] = None) -> List[Dict]:
    """
    Fetches performance data grouped by question difficulty for a specific user.
    """
    query = (
        db.query(
            Question.level.label("difficulty"),
            func.count(UserAnswer.id).label("total_questions"),
            func.sum(case((UserAnswer.is_correct, 1), else_=0)).label("correct_answers"),
            func.sum(UserAnswer.score).label("total_score"),
        )
        .join(Session, UserAnswer.session_id == Session.id)
        .join(User, Session.user_id == User.id)
        .join(Question, UserAnswer.question_id == Question.que_id)
        .filter(User.external_id == user_external_id)
    )

    if assessment_id:
        query = query.filter(Session.assessment_id == assessment_id)

    result = query.group_by(Question.level).order_by(Question.level).all()

    return [
        {
            "difficulty": row.difficulty,
            "total_questions": row.total_questions,
            "correct_answers": row.correct_answers,
            "total_score": row.total_score,
        }
        for row in result
    ]
