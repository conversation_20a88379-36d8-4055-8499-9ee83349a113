"""
This module provides skill-related database operations using SQLAlchemy ORM.
"""

from typing import Dict, List, Optional, Tuple

from app.utils.logger import error, log_database_error
from database.db import get_db_context
from database.models import Question
from database.models import Session as SessionModel
from database.models import <PERSON>ll, User, UserAnswer
from sqlalchemy import and_, case, cast, func
from sqlalchemy.exc import IntegrityError
from sqlalchemy.sql.sqltypes import Numeric


def validate_skills_exist(skill_ids: List[int]) -> Tuple[bool, List[int]]:
    """
    Validate that all skill IDs exist in the database.

    Args:
        skill_ids: List of skill IDs to validate

    Returns:
        Tuple of (all_exist: bool, missing_skills: List[int])
    """
    try:
        with get_db_context() as db:
            existing_skills = db.query(Skill.id).filter(Skill.id.in_(skill_ids)).all()
            existing_skill_ids = [skill.id for skill in existing_skills]

            missing_skills = list(set(skill_ids) - set(existing_skill_ids))
            return len(missing_skills) == 0, missing_skills

    except Exception as e:
        log_database_error("select", "skills", e, skill_ids=skill_ids)
        return False, skill_ids


def valid_skill_description(topic: str) -> bool:
    """
    Check if a topic matches a valid skill description in the database.

    This function checks if the topic exactly matches a skill name or if it starts with a valid skill name
    followed by a timestamp (e.g., "SkillName_DD_MM_YYYY").

    Args:
        topic (str): The topic to validate

    Returns:
        bool: True if the topic matches a valid skill, False otherwise
    """
    try:
        with get_db_context() as db:
            # First try exact match
            exact_match = db.query(Skill).filter(Skill.name == topic).first()
            if exact_match:
                return True

            # If no exact match, check if topic starts with a valid skill name
            # This handles cases where the topic is in the format "SkillName_timestamp"
            skill_names = db.query(Skill.name).all()

            for skill_name_tuple in skill_names:
                skill_name = skill_name_tuple[0]
                if topic.startswith(skill_name + "_"):
                    return True

            return False
    except Exception as e:
        error(f"Error validating skill description: {e}")
        return False


def get_skill_id_by_name(skill_name: str) -> Optional[int]:
    """
    Get skill ID by skill name.

    Args:
        skill_name: Name of the skill to find

    Returns:
        Skill ID if found, None otherwise
    """
    try:
        with get_db_context() as db:
            skill = db.query(Skill.id).filter(Skill.name == skill_name).first()
            return skill.id if skill else None
    except Exception as e:
        error(f"Error getting skill ID by name: {e}")
        return None


def create_skill(name: str, description: str) -> Optional[Dict]:
    """
    Create a new skill in the database.

    Args:
        name: Name of the skill
        description: Description of the skill

    Returns:
        Dictionary with skill data if successful, None otherwise
    """
    try:
        with get_db_context() as db:
            new_skill = Skill(name=name, description=description)
            db.add(new_skill)
            db.flush()  # Flush to get the ID

            skill_dict = {"id": new_skill.id, "name": new_skill.name, "description": new_skill.description}
            return skill_dict
    except IntegrityError:
        raise ValueError("A skill with this name already exists")
    except Exception as e:
        error(f"Error creating skill: {str(e)}")
        return None


def get_skills_paginated(limit: int, offset: int) -> Tuple[List[Dict], int]:
    """
    Get skills with pagination and question counts.

    Args:
        limit: Maximum number of items per page
        offset: Starting position

    Returns:
        Tuple of (skills_list, total_count)
    """
    try:
        with get_db_context() as db:
            # Get total count first
            total = db.query(Skill).count()

            # Get paginated results with question count
            skills_query = (
                db.query(Skill.id, Skill.name, Skill.description, func.count(Question.que_id).label("question_count"))
                .outerjoin(Question, Skill.id == Question.skill_id)
                .group_by(Skill.id, Skill.name, Skill.description)
                .order_by(Skill.name)
                .limit(limit)
                .offset(offset)
            )

            skills = []
            for skill in skills_query:
                skills.append(
                    {
                        "id": skill.id,
                        "name": skill.name,
                        "description": skill.description,
                        "question_count": skill.question_count,
                    }
                )

            return skills, total
    except Exception as e:
        error(f"Error getting skills: {str(e)}")
        return [], 0


def get_skill_by_id(skill_id: int) -> Optional[Dict]:
    """
    Get skill details by ID with question count.

    Args:
        skill_id: ID of the skill

    Returns:
        Dictionary with skill data if found, None otherwise
    """
    try:
        with get_db_context() as db:
            # Get skill details
            skill = db.query(Skill).filter(Skill.id == skill_id).first()

            if not skill:
                return None

            # Get question count for this skill
            question_count = db.query(Question).filter(Question.skill_id == skill_id).count()

            skill_dict = {
                "id": skill.id,
                "name": skill.name,
                "description": skill.description,
                "question_count": question_count,
            }

            return skill_dict
    except Exception as e:
        error(f"Error getting skill by ID: {str(e)}")
        return None


def get_skill_question_counts() -> List[Dict]:
    """
    Get question counts for each skill.

    Returns:
        List of dictionaries with skill_id and count
    """
    try:
        with get_db_context() as db:
            counts = (
                db.query(Question.skill_id, func.count().label("count"))
                .group_by(Question.skill_id)
                .order_by(Question.skill_id)
                .all()
            )

            return [{"skill_id": skill_id, "count": count} for skill_id, count in counts]
    except Exception as e:
        error(f"Error getting skill question counts: {str(e)}")
        return []


def get_question_count_for_skill(skill_id: int) -> Optional[int]:
    """
    Get current question count for a specific skill.

    Args:
        skill_id: ID of the skill

    Returns:
        Question count if skill exists, None if skill not found
    """
    try:
        with get_db_context() as db:
            # First check if skill exists
            skill_exists = db.query(Skill).filter(Skill.id == skill_id).first()
            if not skill_exists:
                return None

            # Get question count
            count = db.query(Question).filter(Question.skill_id == skill_id).count()
            return count
    except Exception as e:
        error(f"Error getting question count for skill: {str(e)}")
        return None


def get_skill_questions_paginated(skill_id: int, limit: int, offset: int) -> Tuple[Optional[Dict], List[Dict], int]:
    """
    Get questions for a skill with pagination.

    Args:
        skill_id: ID of the skill
        limit: Maximum number of items per page
        offset: Starting position

    Returns:
        Tuple of (skill_info, questions_list, total_count)
        skill_info is None if skill not found
    """
    try:
        with get_db_context() as db:
            # First check if the skill exists
            skill = db.query(Skill.id, Skill.name).filter(Skill.id == skill_id).first()
            if not skill:
                return None, [], 0

            skill_dict = {"id": skill.id, "name": skill.name}

            # Get total count of questions for this skill
            total = db.query(Question).filter(Question.skill_id == skill_id).count()

            # Get paginated questions
            questions_query = (
                db.query(
                    Question.que_id,
                    Question.topic,
                    Question.level,
                    Question.question,
                    Question.options,
                    Question.answer,
                )
                .filter(Question.skill_id == skill_id)
                .order_by(Question.que_id)
                .limit(limit)
                .offset(offset)
            )

            questions = []
            for q in questions_query:
                questions.append(
                    {
                        "que_id": q.que_id,
                        "topic": q.topic,
                        "level": q.level,
                        "question": q.question,
                        "options": q.options,
                        "answer": q.answer,
                    }
                )

            return skill_dict, questions, total
    except Exception as e:
        error(f"Error getting skill questions: {str(e)}")
        return None, [], 0


def get_user_skill_metrics() -> List[Dict]:
    """
    Get skill-based metrics for all users.

    Returns:
        List of dictionaries with user and skill performance data
    """
    try:
        with get_db_context() as db:
            # Complex query with multiple joins and aggregations
            results = (
                db.query(
                    User.id.label("user_id"),
                    User.display_name,
                    Skill.id.label("skill_id"),
                    Skill.name.label("skill_name"),
                    func.count(UserAnswer.id).label("total_questions_answered"),
                    func.sum(case((UserAnswer.is_correct, 1), else_=0)).label("correct_answers"),
                    func.sum(UserAnswer.score).label("total_score"),
                    func.round(cast(func.avg(UserAnswer.score), Numeric), 2).label("avg_score"),
                    func.round(
                        (cast(func.sum(case((UserAnswer.is_correct, 1), else_=0)), Numeric) / func.count(UserAnswer.id))
                        * 100,
                        2,
                    ).label("accuracy_percentage"),
                    func.round(
                        (
                            cast(func.sum(case((UserAnswer.is_correct, 1), else_=0)), Numeric)
                            / func.count(UserAnswer.id)
                        ),
                        4,
                    ).label("accuracy_ratio"),
                )
                .join(SessionModel, User.id == SessionModel.user_id)
                .join(UserAnswer, SessionModel.id == UserAnswer.session_id)
                .join(Question, UserAnswer.question_id == Question.que_id)
                .join(Skill, Question.skill_id == Skill.id)
                .group_by(User.id, User.display_name, Skill.id, Skill.name)
                .order_by(User.display_name, Skill.name)
                .all()
            )

            metrics = []
            for row in results:
                metrics.append(
                    {
                        "user_id": row.user_id,
                        "display_name": row.display_name,
                        "skill_id": row.skill_id,
                        "skill_name": row.skill_name,
                        "total_questions_answered": row.total_questions_answered,
                        "correct_answers": row.correct_answers,
                        "total_score": row.total_score,
                        "avg_score": float(row.avg_score) if row.avg_score else 0.0,
                        "accuracy_percentage": float(row.accuracy_percentage) if row.accuracy_percentage else 0.0,
                        "accuracy_ratio": float(row.accuracy_ratio) if row.accuracy_ratio else 0.0,
                    }
                )

            return metrics
    except Exception as e:
        error(f"Error getting user skill metrics: {str(e)}")
        return []


def get_user_skill_metrics_by_email(email: str) -> Tuple[Optional[int], List[Dict]]:
    """
    Get skill-based metrics for a specific user by email.

    Args:
        email: User's email address

    Returns:
        Tuple of (user_id, metrics_list)
        user_id is None if user not found
    """
    try:
        with get_db_context() as db:
            # First, get the user's internal ID
            user = db.query(User.id).filter(User.email == email).first()
            if not user:
                return None, []

            user_id = user.id

            # Get skill metrics for this user
            results = (
                db.query(
                    User.id.label("user_id"),
                    User.display_name,
                    Skill.id.label("skill_id"),
                    Skill.name.label("skill_name"),
                    func.count(UserAnswer.id).label("total_questions_answered"),
                    func.sum(case((UserAnswer.is_correct, 1), else_=0)).label("correct_answers"),
                    func.sum(UserAnswer.score).label("total_score"),
                    func.round(cast(func.avg(UserAnswer.score), Numeric), 2).label("avg_score"),
                    func.round(
                        (cast(func.sum(case((UserAnswer.is_correct, 1), else_=0)), Numeric) / func.count(UserAnswer.id))
                        * 100,
                        2,
                    ).label("accuracy_percentage"),
                    func.round(
                        (
                            cast(func.sum(case((UserAnswer.is_correct, 1), else_=0)), Numeric)
                            / func.count(UserAnswer.id)
                        ),
                        4,
                    ).label("accuracy_ratio"),
                )
                .join(SessionModel, User.id == SessionModel.user_id)
                .join(UserAnswer, SessionModel.id == UserAnswer.session_id)
                .join(Question, UserAnswer.question_id == Question.que_id)
                .join(Skill, Question.skill_id == Skill.id)
                .filter(User.id == user_id)
                .group_by(User.id, User.display_name, Skill.id, Skill.name)
                .order_by(Skill.name)
                .all()
            )

            metrics = []
            for row in results:
                metrics.append(
                    {
                        "user_id": row.user_id,
                        "display_name": row.display_name,
                        "skill_id": row.skill_id,
                        "skill_name": row.skill_name,
                        "total_questions_answered": row.total_questions_answered,
                        "correct_answers": row.correct_answers,
                        "total_score": row.total_score,
                        "avg_score": float(row.avg_score) if row.avg_score else 0.0,
                        "accuracy_percentage": float(row.accuracy_percentage) if row.accuracy_percentage else 0.0,
                        "accuracy_ratio": float(row.accuracy_ratio) if row.accuracy_ratio else 0.0,
                    }
                )

            return user_id, metrics
    except Exception as e:
        error(f"Error getting user skill metrics by email: {str(e)}")
        return None, []


def get_user_skill_performance_detailed(user_id: int) -> List[Dict]:
    """
    Get detailed skill performance data for a specific user including level breakdowns.

    Args:
        user_id: ID of the user

    Returns:
        List of dictionaries with detailed skill performance data
    """
    try:
        with get_db_context() as db:
            results = (
                db.query(
                    User.id.label("user_id"),
                    User.display_name,
                    Skill.id.label("skill_id"),
                    Skill.name.label("skill_name"),
                    func.count(UserAnswer.id).label("total_questions_answered"),
                    func.sum(case((UserAnswer.is_correct, 1), else_=0)).label("correct_answers"),
                    func.sum(UserAnswer.score).label("total_score"),
                    func.round(cast(func.avg(UserAnswer.score), Numeric), 2).label("avg_score"),
                    func.round(
                        (cast(func.sum(case((UserAnswer.is_correct, 1), else_=0)), Numeric) / func.count(UserAnswer.id))
                        * 100,
                        2,
                    ).label("accuracy_percentage"),
                    # Easy level correct/incorrect
                    func.sum(case((and_(Question.level == "easy", UserAnswer.is_correct), 1), else_=0)).label(
                        "easy_correct"
                    ),
                    func.sum(case((and_(Question.level == "easy", UserAnswer.is_correct), 1), else_=0)).label(
                        "easy_incorrect"
                    ),
                    # Intermediate level correct/incorrect
                    func.sum(case((and_(Question.level == "intermediate", UserAnswer.is_correct), 1), else_=0)).label(
                        "intermediate_correct"
                    ),
                    func.sum(case((and_(Question.level == "intermediate", UserAnswer.is_correct), 1), else_=0)).label(
                        "intermediate_incorrect"
                    ),
                    # Advanced level correct/incorrect
                    func.sum(case((and_(Question.level == "advanced", UserAnswer.is_correct), 1), else_=0)).label(
                        "advanced_correct"
                    ),
                    func.sum(case((and_(Question.level == "advanced", UserAnswer.is_correct), 1), else_=0)).label(
                        "advanced_incorrect"
                    ),
                )
                .join(SessionModel, UserAnswer.session_id == SessionModel.id)
                .join(User, SessionModel.user_id == User.id)
                .join(Question, UserAnswer.question_id == Question.que_id)
                .join(Skill, Question.skill_id == Skill.id)
                .filter(User.id == user_id)
                .group_by(User.id, User.display_name, Skill.id, Skill.name)
                .order_by(Skill.name)
                .all()
            )

            skill_performance = []
            for row in results:
                skill_performance.append(
                    {
                        "user_id": row.user_id,
                        "display_name": row.display_name,
                        "skill_id": row.skill_id,
                        "skill_name": row.skill_name,
                        "total_questions_answered": int(row.total_questions_answered),
                        "correct_answers": int(row.correct_answers),
                        "total_score": float(row.total_score) if row.total_score else 0.0,
                        "avg_score": float(row.avg_score) if row.avg_score else 0.0,
                        "accuracy_percentage": float(row.accuracy_percentage) if row.accuracy_percentage else 0.0,
                        "easy_correct": int(row.easy_correct),
                        "easy_incorrect": int(row.easy_incorrect),
                        "intermediate_correct": int(row.intermediate_correct),
                        "intermediate_incorrect": int(row.intermediate_incorrect),
                        "advanced_correct": int(row.advanced_correct),
                        "advanced_incorrect": int(row.advanced_incorrect),
                    }
                )

            return skill_performance
    except Exception as e:
        error(f"Error getting detailed user skill performance: {str(e)}")
        return []


def get_user_skill_performance_detailed_by_email(email: str) -> Tuple[Optional[int], List[Dict]]:
    """
    Get detailed skill performance data for a specific user by email including level breakdowns.

    Args:
        email: User's email address

    Returns:
        Tuple of (user_id, performance_data)
        user_id is None if user not found
    """
    try:
        with get_db_context() as db:
            # First, get the user's internal ID
            user = db.query(User.id).filter(User.email == email).first()
            if not user:
                return None, []

            user_id = user.id

            results = (
                db.query(
                    User.id.label("user_id"),
                    User.display_name,
                    Skill.id.label("skill_id"),
                    Skill.name.label("skill_name"),
                    func.count(UserAnswer.id).label("total_questions_answered"),
                    func.sum(case((UserAnswer.is_correct, 1), else_=0)).label("correct_answers"),
                    func.sum(UserAnswer.score).label("total_score"),
                    func.round(cast(func.avg(UserAnswer.score), Numeric), 2).label("avg_score"),
                    func.round(
                        (cast(func.sum(case((UserAnswer.is_correct, 1), else_=0)), Numeric) / func.count(UserAnswer.id))
                        * 100,
                        2,
                    ).label("accuracy_percentage"),
                    # Easy level correct/incorrect
                    func.sum(case((and_(Question.level == "easy", UserAnswer.is_correct), 1), else_=0)).label(
                        "easy_correct"
                    ),
                    func.sum(case((and_(Question.level == "easy", UserAnswer.is_correct), 1), else_=0)).label(
                        "easy_incorrect"
                    ),
                    # Intermediate level correct/incorrect
                    func.sum(case((and_(Question.level == "intermediate", UserAnswer.is_correct), 1), else_=0)).label(
                        "intermediate_correct"
                    ),
                    func.sum(case((and_(Question.level == "intermediate", UserAnswer.is_correct), 1), else_=0)).label(
                        "intermediate_incorrect"
                    ),
                    # Advanced level correct/incorrect
                    func.sum(case((and_(Question.level == "advanced", UserAnswer.is_correct), 1), else_=0)).label(
                        "advanced_correct"
                    ),
                    func.sum(case((and_(Question.level == "advanced", UserAnswer.is_correct), 1), else_=0)).label(
                        "advanced_incorrect"
                    ),
                )
                .join(SessionModel, UserAnswer.session_id == SessionModel.id)
                .join(User, SessionModel.user_id == User.id)
                .join(Question, UserAnswer.question_id == Question.que_id)
                .join(Skill, Question.skill_id == Skill.id)
                .filter(User.id == user_id)
                .group_by(User.id, User.display_name, Skill.id, Skill.name)
                .order_by(Skill.name)
                .all()
            )

            skills = []
            for row in results:
                skills.append(
                    {
                        "user_id": row.user_id,
                        "display_name": row.display_name,
                        "skill_id": row.skill_id,
                        "skill_name": row.skill_name,
                        "total_questions_answered": int(row.total_questions_answered),
                        "correct_answers": int(row.correct_answers),
                        "total_score": float(row.total_score) if row.total_score else 0.0,
                        "avg_score": float(row.avg_score) if row.avg_score else 0.0,
                        "accuracy_percentage": float(row.accuracy_percentage) if row.accuracy_percentage else 0.0,
                        "easy_correct": int(row.easy_correct),
                        "easy_incorrect": int(row.easy_incorrect),
                        "intermediate_correct": int(row.intermediate_correct),
                        "intermediate_incorrect": int(row.intermediate_incorrect),
                        "advanced_correct": int(row.advanced_correct),
                        "advanced_incorrect": int(row.advanced_incorrect),
                    }
                )

            return user_id, skills
    except Exception as e:
        error(f"Error getting detailed user skill performance by email: {str(e)}")
        return None, []
