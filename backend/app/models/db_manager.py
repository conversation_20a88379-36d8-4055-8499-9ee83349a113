"""
This module provides functionalities for setting up a PostgreSQL database
and managing `questions` and `user_assessment` tables with synchronous operations.
"""

from database.db import get_db_context
from database.models import UserAssessment
from dotenv import load_dotenv
from sqlalchemy.exc import SQLAlchemyError

from ..utils.logger import log_database_error

load_dotenv()


def insert_user_data(data: dict):
    """
    Insert user quiz assessment data into the `user_assessment` table.

    Args:
        data (dict): A dictionary with the user's assessment data.

    Raises:
        ValueError: If `data` is not a dictionary.

    Notes:
        - Stores user-specific data like quiz type, answers, score, etc.
        - Options are stored as JSONB for efficient querying.
    """
    if not isinstance(data, dict):
        raise ValueError("`data` must be a dictionary.")

    try:
        with get_db_context() as db:
            user_assessment = UserAssessment(
                user_id=data["user_id"],
                topic=data["topic"],
                level=data["level"],
                quiz_type=data["quiz_type"],
                que_id=data["que_id"],
                question=data["question"],
                options=data["options"],
                correct_answer=data["correct_answer"],
                user_answer=data["user_answer"],
                result=data["result"],
                score=data["score"],
            )
            db.add(user_assessment)
    except SQLAlchemyError as e:
        log_database_error("insert", "user_assessment", e)
    except Exception as e:
        log_database_error("insert", "user_assessment", e)


def get_performance_level(obtained_score: int, total_score: int) -> str:
    """
    Determine performance level based on obtained score percentage.
    Consolidated from db_manager.py and assessment_manager.py
    """
    if obtained_score < 0 or obtained_score > total_score:
        return "Invalid score"

    percentage = (obtained_score / total_score) * 100 if total_score > 0 else 0

    levels = [
        (0, "Fail"),
        (33, "Basic"),
        (62, "Acceptable"),
        (85, "Exceed Expectation"),
        (100, "OUTSTANDING"),
    ]

    if percentage == 0 and obtained_score == 0:
        return "Fail"

    performance = "Fail"
    for threshold, level in levels:
        if percentage >= threshold:
            performance = level
        else:
            break

    if percentage == 100:
        performance = "OUTSTANDING"

    return performance


def calculate_total_score(easy_attempted: int, intermediate_attempted: int, advanced_attempted: int) -> int:
    """
    Calculate total score based on attempted questions in different difficulty levels.
    Consolidated from db_manager.py and assessment_manager.py
    """
    return (easy_attempted * 1) + (intermediate_attempted * 2) + (advanced_attempted * 3)


def calculate_percentage(obtained: int, total: int, quiz_type: str) -> str:
    """
    Calculate percentage for 'final' quizzes only.
    Consolidated from db_manager.py and assessment_manager.py
    """
    if quiz_type == "final":
        return f"{round((obtained / total) * 100, 2)}%" if total > 0 else "0%"
    return ""
