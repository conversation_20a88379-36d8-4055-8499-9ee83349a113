"""
Session Management Utility Functions

This module provides reusable session management utilities
to reduce code duplication and improve maintainability. It acts as a
high-level interface over the data access layer (sessions_manager).
"""

from typing import Any, Dict

from fastapi import HTTPException, status

from ..models.sessions_manager import (
    get_session_attempted_questions_count,
    get_session_details,
    get_session_score,
    get_session_user_id,
)
from .logger import warning


def validate_session_ownership(session_code: str, user_id: str) -> bool:
    """
    Validate that the requesting user is the legitimate owner of a given session.

    This is a critical security function to prevent one user from accessing
    or modifying another user's session data.

    Args:
        session_code: The unique identifier for the session.
        user_id: The ID of the user making the request (e.g., from an auth token).

    Returns:
        True if the provided user_id matches the user_id stored in the session.
        False otherwise.

    Raises:
        HTTPException (404 Not Found): If the session_code does not correspond to any active session.
    """
    # Step 1: Retrieve the user ID that is stored as the owner of this session.
    session_user_id = get_session_user_id(session_code)

    # Step 2: Handle the case where the session does not exist.
    # If get_session_user_id returns None or an empty value, the session is invalid.
    if not session_user_id:
        # A 404 is appropriate because the requested resource (the session) was not found.
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Session not found")

    # Step 3: Compare the owner's ID with the requesting user's ID.
    if session_user_id != user_id:
        # For security purposes, log a warning if there's an ownership mismatch.
        # This can help detect unauthorized access attempts.
        warning(f"Session ownership mismatch: session belongs to {session_user_id}, request from {user_id}")

    # Step 4: Return a boolean indicating whether the IDs match.
    # The calling function can then decide how to act on a False result (e.g., raise a 403 Forbidden).
    return session_user_id == user_id


def get_session_remaining_time(session_code: str) -> int:
    """
    Calculate the remaining time for an active session in seconds.

    Args:
        session_code: The unique identifier for the session.

    Returns:
        The remaining time in seconds as an integer. Returns 0 if the session
        is not found or if the time has already expired.
    """
    # Retrieve the full details of the session from the data store.
    session_details = get_session_details(session_code)

    # If the session doesn't exist, there is no remaining time.
    # Returning 0 provides a safe default and avoids NoneType errors in calling code.
    if not session_details:
        return 0

    # Safely access the 'remaining_time_seconds' key from the session data.
    # The .get() method with a default value of 0 prevents a KeyError if the key
    # is missing for any reason, making the function more robust.
    return session_details.get("remaining_time_seconds", 0)


def is_session_valid(session_code: str) -> bool:
    """
    Check if a session is currently valid and active for further interaction.

    A session might be invalid if it doesn't exist, has been completed, or has expired.

    Args:
        session_code: The unique identifier for the session to validate.

    Returns:
        True if the session is valid and can be continued, False otherwise.
    """
    # Retrieve all details for the given session.
    session_details = get_session_details(session_code)

    # A non-existent session is inherently invalid.
    if not session_details:
        return False

    # Check if the session has been marked as completed.
    # A completed session is no longer valid for actions like answering questions.
    if session_details.get("is_completed", False):
        return False

    # If all checks pass, the session is considered valid.
    return True


def get_session_questions_data(session_code: str) -> Dict[str, Any]:
    """
    Aggregate and return a comprehensive snapshot of a session's state.

    This function acts as a convenient wrapper to gather all relevant session
    data in a single call, which is useful for API endpoints that need to
    display a full summary to the user.

    Args:
        session_code: The unique identifier for the session to look up.

    Returns:
        A dictionary containing key details about the session.
        Returns an empty dictionary if the session is not found.
    """
    # First, fetch the core session details. This is the primary data source.
    session_details = get_session_details(session_code)

    # If the session doesn't exist, return an empty dictionary to indicate
    # that no data could be found, which the caller can easily check.
    if not session_details:
        return {}

    # Assemble a dictionary by calling other utility functions and using the
    # fetched session details. This provides a complete and consistent view.
    return {
        "session_details": session_details,  # The raw session data object.
        # The user's current score.
        "current_score": get_session_score(session_code),
        # How many questions were answered.
        "attempted_questions_count": get_session_attempted_questions_count(session_code),
        # Time left in the session.
        "remaining_time_seconds": get_session_remaining_time(session_code),
        # A boolean flag indicating if the session is active.
        "is_valid": is_session_valid(session_code),
    }
