import time
from typing import Dict, List

from fastapi import Request, status
from fastapi.exceptions import HTTPException

# Assuming this is a custom utility function for raising exceptions consistently.
# For this example, we'll define a simple version of it.


def raise_http_exception(status_code: int, detail: str):
    """A helper to raise FastAPI's HTTPException."""
    raise HTTPException(status_code=status_code, detail=detail)


class RateLimiter:
    """
    A simple in-memory rate limiter implemented as a callable class for FastAPI.

    This class tracks the timestamps of recent requests from each client IP address
    to enforce a request limit over a defined time window (a "sliding window" algorithm).

    It is designed to be used as a FastAPI dependency, where an instance is created
    once and then shared across multiple endpoints.
    """

    def __init__(self, requests_per_minute: int = 60):
        """
        Initializes the RateLimiter.

        Args:
            requests_per_minute: The maximum number of requests allowed from a single
                                 IP address within a 60-second window.
        """
        # The maximum number of requests allowed in the time window.
        self.requests_per_minute = requests_per_minute

        # A dictionary to store request history.
        # Key: client IP address (str)
        # Value: a list of timestamps (float) of their recent requests.
        self.request_history: Dict[str, List[float]] = {}

        # How often (in seconds) the cleanup task should run to purge old data.
        # This prevents the cleanup from running on every single request, which is more efficient.
        self.cleanup_interval: int = 60

        # Stores the timestamp of the last time the cleanup task was executed.
        self.last_cleanup: float = time.time()

    async def __call__(self, request: Request):
        """
        This method makes the class instance callable. FastAPI executes this for each
        request when the class instance is used as a dependency.

        It checks the request history for the client's IP and raises an exception
        if the rate limit has been exceeded.

        Args:
            request: The incoming FastAPI Request object, used to get the client's IP.

        Raises:
            HTTPException (429 Too Many Requests): If the client has exceeded the rate limit.
        """
        # Get the current time once to ensure consistency in all comparisons.
        current_time = time.time()

        # --- Periodic Cleanup ---
        # To prevent the self.request_history dictionary from growing indefinitely with old IPs,
        # we periodically run a cleanup task. This is more efficient than cleaning on every request.
        if current_time - self.last_cleanup > self.cleanup_interval:
            await self._cleanup(current_time)
            self.last_cleanup = current_time

        # Get the client's IP address. This acts as the unique identifier for rate limiting.
        client_ip = request.client.host

        # --- Rate Limiting Logic ---
        if client_ip in self.request_history:
            # If we have a record for this IP, we need to check their request history.
            requests = self.request_history[client_ip]

            # This is the core of the "sliding window" algorithm. We create a new list
            # containing only the timestamps from the last 60 seconds.
            # This effectively "forgets" requests that are older than the window.
            recent_requests = [t for t in requests if current_time - t < 60]

            # Check if the number of recent requests has reached or exceeded the limit.
            if len(recent_requests) >= self.requests_per_minute:
                # If so, raise an HTTP 429 error, which is the standard for rate limiting.
                raise_http_exception(
                    status_code=status.HTTP_429_TOO_MANY_REQUESTS, detail="Too many requests. Please try again later."
                )

            # If the user is not rate-limited, add the current request's timestamp to their history.
            self.request_history[client_ip] = recent_requests + [current_time]
        else:
            # If this is the first time we've seen this IP (in a while),
            # create a new entry for them in the history with the current timestamp.
            self.request_history[client_ip] = [current_time]

    async def _cleanup(self, current_time: float):
        """
        Removes old entries from the request history to prevent memory leaks.

        This method iterates through all tracked IPs and removes request timestamps
        that are older than the 60-second window. If an IP has no recent requests
        left, its entry is removed from the dictionary entirely.

        Args:
            current_time: The current timestamp, passed in for efficiency.
        """
        # We iterate over a copy of the keys (`list(self.request_history.keys())`)
        # because we cannot modify the size of a dictionary while iterating over it.
        # Deleting a key would otherwise raise a RuntimeError.
        for ip in list(self.request_history.keys()):
            # Filter the list of timestamps for the current IP, keeping only the recent ones.
            self.request_history[ip] = [t for t in self.request_history[ip] if current_time - t < 60]

            # If, after filtering, the list of requests for this IP is empty,
            # we can remove the IP from our history dictionary to save memory.
            if not self.request_history[ip]:
                del self.request_history[ip]


# --- Instantiation ---
# Create a single, global instance of the RateLimiter.
# This instance will be imported and used as a dependency in FastAPI routes.
# It's crucial that it's a single instance so that it maintains a shared state
# (the request_history) for all requests handled by the application.
rate_limiter = RateLimiter(requests_per_minute=60)
