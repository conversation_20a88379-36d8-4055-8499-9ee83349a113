"""
Validation Utility Functions

This module provides reusable validation utilities
to reduce code duplication and improve maintainability.
"""

import re
from typing import Any, Dict, List, Optional, Union

from fastapi import HTTPException, status

# Imports constants like regex patterns, valid modes, etc.
from ..config import config

# These are assumed to be local utility modules
from .db_utils import exists_in_table
from .logger import debug, error


def validate_required_fields(data: Dict[str, Any], required_fields: List[str]) -> None:
    """
    Validate that required fields are present and not None in the input data.

    Args:
        data: The dictionary (e.g., from a JSON request body) to validate.
        required_fields: A list of strings representing the keys that must be present.

    Raises:
        HTTPException (400 Bad Request): If any of the required fields are missing or their value is None.
    """
    # Use a list comprehension to efficiently find all fields that are either
    # not in the data dictionary or are present but have a value of None.
    # This check is crucial for ensuring mandatory data is actually provided.
    missing_fields = [field for field in required_fields if field not in data or data[field] is None]

    # If the list of missing fields is not empty, it means validation has failed.
    if missing_fields:
        # Log the specific error for debugging purposes on the server-side.
        error(f"Missing required fields: {missing_fields}")
        # Raise an HTTP 400 error with a user-friendly message,
        # clearly indicating which fields are missing.
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail=f"Missing required fields: {', '.join(missing_fields)}"
        )


def validate_string_length(value: str, field_name: str, min_length: int = 1, max_length: Optional[int] = None) -> None:
    """
    Validate the length of a string value.

    Args:
        value: The string value to validate.
        field_name: The name of the field, used for creating clear error messages.
        min_length: The minimum required length of the string.
        max_length: The optional maximum allowed length of the string.

    Raises:
        HTTPException (400 Bad Request): If the value is not a string or its length is outside the defined bounds.
    """
    # First, ensure the value is actually a string before attempting length checks.
    if not isinstance(value, str):
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=f"{field_name} must be a string")

    # Check if the string's length meets the minimum requirement.
    if len(value) < min_length:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"{field_name} must be at least {min_length} characters long",
        )

    # If a maximum length is specified, check if the string exceeds it.
    if max_length and len(value) > max_length:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail=f"{field_name} must be at most {max_length} characters long"
        )


def validate_email_format(email: str) -> bool:
    """
    Validate email format using a regular expression.

    Args:
        email: The email address string to validate.

    Returns:
        True if the email format is valid, False otherwise.
    """
    # An empty string is not a valid email.
    if not email:
        return False

    # Retrieve the standard email regex pattern from the central constants file.
    # This promotes reusability and makes it easy to update the pattern in one place.
    email_pattern = config.EMAIL_PATTERN

    # Use re.match() to check if the pattern matches from the beginning of the string.
    # It returns a match object (truthy) on success, and None (falsy) on failure.
    # The `is not None` makes the boolean return explicit.
    return re.match(email_pattern, email) is not None


def validate_skill_ids(skill_ids: List[int]) -> None:
    """
    Validate that a list of skill IDs are valid integers and exist in the database.

    Args:
        skill_ids: A list of skill IDs to validate.

    Raises:
        HTTPException (400 Bad Request): If the list is empty, or if any skill ID is not a positive integer
                                         or does not exist in the 'skills' table.
    """
    # An assessment must be associated with at least one skill.
    if not skill_ids:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="At least one skill ID is required")

    # Iterate through each provided skill ID to validate it individually.
    for skill_id in skill_ids:
        # Each ID must be an integer and should be positive.
        if not isinstance(skill_id, int) or skill_id <= 0:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=f"Invalid skill ID format: {skill_id}")

        # Check against the database to ensure the skill actually exists.
        # This prevents creating associations to non-existent records.
        if not exists_in_table("skills", {"id": skill_id}):
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=f"Skill ID {skill_id} not found")


def validate_assessment_id(assessment_id: Union[int, str]) -> int:
    """
    Validate that an assessment ID is a valid integer and exists in the database.

    Args:
        assessment_id: The assessment ID, which may come as a string from a URL path.

    Returns:
        The validated and converted assessment ID as an integer.

    Raises:
        HTTPException (400 Bad Request): If the ID cannot be converted to an integer.
        HTTPException (404 Not Found): If the assessment ID does not exist in the 'assessments' table.
    """
    # Use a try-except block to safely convert the ID to an integer.
    # This handles cases where the ID might be a non-numeric string like "abc".
    try:
        assessment_id = int(assessment_id)
    except (ValueError, TypeError):
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid assessment ID format")

    # After confirming the format, check if an assessment with this ID exists in the database.
    if not exists_in_table("assessments", {"id": assessment_id}):
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"Assessment ID {assessment_id} not found")

    # Return the validated, integer-typed ID.
    return assessment_id


def validate_user_id(user_id: str) -> None:
    """
    Validate a user ID's format and existence in the database.

    Args:
        user_id: The external user ID to validate.

    Raises:
        HTTPException (400 Bad Request): If the user ID is missing or not a string.
    """
    # A user ID must be provided and must be a string.
    if not user_id or not isinstance(user_id, str):
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="User ID is required and must be a string")

    # Check if the user exists in our database. Note that this function
    # currently only logs if the user is not found, it does not raise an
    # exception. This might be intentional if the system is designed to
    # create users on-the-fly (lazy creation).
    if not exists_in_table("users", {"external_id": user_id}):
        debug(f"User {user_id} not found in database")


def validate_question_selection_mode(mode: str) -> None:
    """
    Validate that the question selection mode is one of the allowed values.

    Args:
        mode: The selection mode string (e.g., "auto", "manual").

    Raises:
        HTTPException (400 Bad Request): If the mode is not in the list of valid modes.
    """
    # Fetch the list of valid modes from the constants file. This is good practice
    # as it centralizes configuration and avoids hardcoding values.
    valid_modes = config.VALID_MODES

    # Check if the provided mode is a member of the valid modes list.
    if mode not in valid_modes:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid question selection mode. Must be one of: {', '.join(valid_modes)}",
        )


def validate_difficulty_level(level: str) -> None:
    """
    Validate that the difficulty level is one of the allowed values.

    Args:
        level: The difficulty level string (e.g., "easy", "medium", "hard").

    Raises:
        HTTPException (400 Bad Request): If the level is not in the list of valid levels.
    """
    # Retrieve the allowed difficulty levels from the constants file.
    valid_levels = config.VALID_LEVELS

    # The .lower() call makes the validation case-insensitive, so "Easy" or "EASY"
    # would also be accepted if "easy" is in the valid_levels list.
    if level.lower() not in valid_levels:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid difficulty level. Must be one of: {', '.join(valid_levels)}",
        )


def validate_duration(duration: int, min_duration: int = 1, max_duration: int = 180) -> None:
    """
    Validate the duration of an assessment, ensuring it's an integer within a specific range.

    Args:
        duration: The duration in minutes.
        min_duration: The minimum allowed duration.
        max_duration: The maximum allowed duration.

    Raises:
        HTTPException (400 Bad Request): If the duration is not an integer or is outside the allowed range.
    """
    # This single check efficiently validates the type and the range.
    # It ensures duration is an integer and is between the min and max values (inclusive).
    if not isinstance(duration, int) or not (min_duration <= duration <= max_duration):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Duration must be an integer between {min_duration} and {max_duration} minutes",
        )


def validate_json_structure(data: Any, required_keys: List[str]) -> None:
    """
    Validate that the input data is a dictionary and contains a set of required keys.

    Args:
        data: The data to validate (expected to be a dictionary).
        required_keys: A list of keys that must be present in the dictionary.

    Raises:
        HTTPException (400 Bad Request): If the data is not a dictionary or is missing required keys.
    """
    # First, ensure the provided data is a dictionary.
    if not isinstance(data, dict):
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Data must be a valid JSON object")

    # Use a list comprehension to find which of the required keys are not in the data.
    missing_keys = [key for key in required_keys if key not in data]

    # If the list of missing keys is not empty, raise an error.
    if missing_keys:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail=f"Missing required keys: {', '.join(missing_keys)}"
        )


def validate_pagination_params(limit: int, offset: int) -> None:
    """
    Validate pagination parameters 'limit' and 'offset' to ensure they are reasonable.

    Args:
        limit: The number of items to return per page.
        offset: The starting index for the items to return.

    Raises:
        HTTPException (400 Bad Request): If limit or offset values are invalid.
    """
    # The 'limit' must be positive to be meaningful and is capped at 100
    # to prevent clients from requesting excessively large amounts of data.
    if not (0 < limit <= 100):
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Limit must be between 1 and 100")

    # The 'offset' must be zero or a positive number, as a negative offset is invalid.
    if offset < 0:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Offset must be non-negative")


def sanitize_input(input_string: str, max_length: int = 1000) -> str:
    """
    Sanitize an input string by removing potentially harmful characters and enforcing a max length.
    This is a basic defense against Cross-Site Scripting (XSS) and other injection attacks.

    Args:
        input_string: The string to sanitize.
        max_length: The maximum allowed length for the final string.

    Returns:
        The sanitized and truncated string.
    """
    # If the input isn't a string, return an empty string to avoid errors.
    if not isinstance(input_string, str):
        return ""

    # Use a regular expression to remove common characters used in XSS attacks.
    # This is a blacklist approach; a whitelist (allowing only specific characters) is often safer.
    sanitized = re.sub(r'[<>"\';()&+]', "", input_string)

    # Truncate the string to a maximum length to prevent buffer overflow-style issues
    # or overly long inputs from straining the system.
    if len(sanitized) > max_length:
        sanitized = sanitized[:max_length]

    # Remove any leading or trailing whitespace from the final string.
    return sanitized.strip()


def validate_skill_name(skill_name: str) -> None:
    """
    Perform comprehensive validation for a skill name, checking length, characters, and uniqueness.

    Args:
        skill_name: The name of the skill to validate.

    Raises:
        HTTPException (400 Bad Request): If the name format is invalid.
        HTTPException (409 Conflict): If a skill with the same name already exists.
    """
    # Reuse the existing length validation utility for consistency.
    validate_string_length(skill_name, "Skill name", min_length=2, max_length=100)

    # Define a regex pattern for allowed characters in a skill name.
    # Here, it allows letters, numbers, spaces, hyphens, underscores, and periods.
    if not re.match(r"^[a-zA-Z0-9\s\-_\.]+$", skill_name):
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Skill name contains invalid characters")

    # Check the database to ensure the skill name is unique.
    # This prevents duplicate skills from being created.
    # A 409 Conflict is the appropriate HTTP status for a duplicate resource.
    if exists_in_table("skills", {"name": skill_name}):
        raise HTTPException(status_code=status.HTTP_409_CONFLICT, detail=f"Skill '{skill_name}' already exists")


def validate_assessment_name(assessment_name: str) -> None:
    """
    Validate the format of an assessment name.

    Args:
        assessment_name: The name of the assessment to validate.

    Raises:
        HTTPException (400 Bad Request): If the name format is invalid.
    """
    # Reuse the string length validator.
    validate_string_length(assessment_name, "Assessment name", min_length=3, max_length=200)

    # Check for invalid characters using a regex pattern similar to the one for skill names.
    if not re.match(r"^[a-zA-Z0-9\s\-_\.]+$", assessment_name):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Assessment name contains invalid characters"
        )
