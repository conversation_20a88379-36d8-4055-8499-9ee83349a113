"""
Performance Calculation Utility Functions

This module provides reusable performance calculation utilities
to reduce code duplication and improve maintainability. It contains functions
for scoring, calculating percentages, and deriving analytical insights from
raw performance data.
"""

import os
from typing import Dict, List, Optional

# Assumes a central constants file for managing configurable values like scoring.
from ..config import config

# Imports functions from a data access layer responsible for raw database queries.
from ..models.performance_manager import (
    db_get_difficulty_wise_performance,
    db_get_overall_performance,
    db_get_skill_performance,
)


def calculate_question_score(level: str, result: str) -> int:
    """
    Calculate the score for a single question based on its difficulty and result.

    Args:
        level: The question's difficulty level (e.g., "easy", "intermediate", "advanced").
        result: The outcome of the user's answer (e.g., "correct", "incorrect", "timeout").

    Returns:
        The integer score awarded for the question.
    """
    # A guard clause to handle non-scoring results first.
    # If the answer was incorrect or timed out, the score is always zero.
    if result.lower() in ("timeout", "incorrect"):
        return 0

    # Retrieve the score mapping from the central constants file.
    # This centralizes the scoring rules, making them easy to change.
    score_mapping = config.SCORE_MAPPING

    # Look up the score for the given difficulty level.
    # .get(key, 0) is used for safety: if the level is unknown or misspelled, it defaults to 0.
    # .lower() makes the lookup case-insensitive.
    return score_mapping.get(level.lower(), 0)


def get_max_possible_score() -> int:
    """
    Calculate the maximum possible score for a standard assessment.

    This function reads the question count for each difficulty level from
    environment variables to determine the total possible score.

    Returns:
        The maximum possible score as an integer.
    """
    # Fetch question counts from environment variables.
    # This makes the assessment structure configurable without changing the code.
    # Default values ("6", "8") are used as fallbacks if the variables are not set.
    easy_count = int(os.getenv("EASY_QUESTIONS_COUNT", "6"))
    intermediate_count = int(os.getenv("INTERMEDIATE_QUESTIONS_COUNT", "6"))
    advanced_count = int(os.getenv("ADVANCED_QUESTIONS_COUNT", "8"))

    # Calculate the max score by multiplying the count of each question type
    # by its corresponding score (assuming easy=1, intermediate=2, advanced=3).
    max_score = (easy_count * 1) + (intermediate_count * 2) + (advanced_count * 3)

    return max_score


def calculate_performance_percentage(obtained_score: int, max_score: Optional[int] = None) -> float:
    """
    Calculate performance as a percentage.

    Args:
        obtained_score: The score achieved by the user.
        max_score: The maximum possible score. If not provided, it will be calculated.

    Returns:
        The user's performance as a percentage (e.g., 85.5).
    """
    # If a max_score isn't provided, calculate it using the helper function.
    # This makes the function more flexible for callers.
    if max_score is None:
        max_score = get_max_possible_score()

    # A critical check to prevent a ZeroDivisionError if max_score is 0.
    if max_score == 0:
        return 0.0

    return (obtained_score / max_score) * 100


def get_performance_level(obtained_score, total_score):
    """
    Determine the performance level based on the obtained score percentage.

    Args:
        obtained_score (int): The score obtained by the user.
        total_score (int): The maximum possible score.

    Returns:
        str: The performance level (Fail, Basic, Acceptable, Exceed Expectation, OUTSTANDING).
    """
    if obtained_score < 0 or obtained_score > total_score:
        return "Invalid score"

    percentage = (obtained_score / total_score) * 100 if total_score > 0 else 0

    levels = [
        (0, "Fail"),
        (33, "Basic"),
        (62, "Acceptable"),
        (85, "Exceed Expectation"),
        (100, "OUTSTANDING"),
    ]

    # Corrected logic for level determination
    # Handles the case where total_score might be 0 or obtained_score is 0
    if percentage == 0 and obtained_score == 0:
        return "Fail"

    performance = "Fail"  # Default
    for threshold, level in levels:
        if percentage >= threshold:  # Check if percentage is greater than or equal to threshold
            performance = level
        else:  # if it's less, then the previous level was correct
            break

    # Special case for 100%
    if percentage == 100:
        performance = "OUTSTANDING"

    return performance


def calculate_accuracy_percentage(correct_answers: int, total_questions: int) -> float:
    """
    Calculate the accuracy of answers as a percentage.

    Args:
        correct_answers: The number of questions answered correctly.
        total_questions: The total number of questions attempted.

    Returns:
        The accuracy as a percentage.
    """
    # Prevent ZeroDivisionError if no questions were attempted.
    if total_questions == 0:
        return 0.0

    return (correct_answers / total_questions) * 100


def get_skill_performance_data(user_id: str, assessment_id: Optional[int] = None) -> List[Dict]:
    """
    Fetch and process skill-level performance data for a user from the database.

    Args:
        cur: The active database cursor for executing the query.
        user_id: The ID of the user to get performance for.
        assessment_id: Optional assessment ID to filter results for a specific assessment.

    Returns:
        A list of dictionaries, each representing the performance for a single skill.
    """
    # Step 1: Fetch raw data from the database using the data access layer.
    results = db_get_skill_performance(user_id, assessment_id)

    # Step 2: Process the raw results into a structured and correctly-typed format.
    # This transformation logic belongs in the utility layer.
    processed_results = []
    for result in results:
        processed_results.append(
            {
                "skill_name": result["skill_name"],
                "total_questions": int(result["total_questions"]),
                "correct_answers": int(result["correct_answers"]),
                "accuracy_percentage": (
                    float(result["accuracy_percentage"]) if result["accuracy_percentage"] is not None else 0.0
                ),
                "total_score": float(result["total_score"]) if result["total_score"] is not None else 0.0,
            }
        )
    return processed_results


def get_overall_performance_summary(user_id: str, assessment_id: Optional[int] = None) -> Dict:
    """
    Fetch and compile an overall performance summary for a user.

    Args:
        cur: The active database cursor.
        user_id: The ID of the user to get the summary for.
        assessment_id: Optional assessment ID to filter by.

    Returns:
        A single dictionary containing the overall performance summary.
    """
    # Fetch the aggregated raw data from the database.
    data = db_get_overall_performance(user_id, assessment_id)

    # If no performance data is found, return a default "zeroed-out" dictionary.
    # This prevents errors in the calling code which expects a dictionary with specific keys.
    if not data:
        return {
            "total_questions": 0,
            "correct_answers": 0,
            "total_score": 0.0,
            "accuracy_percentage": 0.0,
            "performance_level": "No Data",
            "average_time_taken": 0.0,
        }

    # Cast the raw data (often strings from the DB) to their correct Python types.
    total_questions = int(data["total_questions"])
    correct_answers = int(data["correct_answers"])
    total_score = float(data["total_score"]) if data["total_score"] is not None else 0.0

    # Build the final summary object, reusing other utility functions for consistency.
    return {
        "total_questions": total_questions,
        "correct_answers": correct_answers,
        "total_score": total_score,
        "accuracy_percentage": calculate_accuracy_percentage(correct_answers, total_questions),
        "performance_level": get_performance_level(int(total_score)),
        "average_time_taken": float(data["average_time_taken"]) if data["average_time_taken"] is not None else 0.0,
    }


def get_difficulty_wise_performance(user_id: str, assessment_id: Optional[int] = None) -> Dict:
    """
    Get a performance breakdown for a user, grouped by question difficulty.

    Args:
        cur: The active database cursor.
        user_id: The ID of the user to get performance for.
        assessment_id: Optional assessment ID to filter by.

    Returns:
        A dictionary where keys are difficulty levels ("easy", "medium", "hard")
        and values are their corresponding performance data.
    """
    # Fetch the raw, grouped results from the database.
    results = db_get_difficulty_wise_performance(user_id, assessment_id)

    # Transform the list of results into a structured dictionary for easy lookups.
    # e.g., {"easy": {...}, "advanced": {...}}
    difficulty_performance = {}
    for result in results:
        difficulty = result["difficulty"]
        total_questions = int(result["total_questions"])
        correct_answers = int(result["correct_answers"])
        difficulty_performance[difficulty] = {
            "total_questions": total_questions,
            "correct_answers": correct_answers,
            "total_score": float(result["total_score"]) if result["total_score"] is not None else 0.0,
            "accuracy_percentage": calculate_accuracy_percentage(correct_answers, total_questions),
        }
    return difficulty_performance


def get_weakest_skill(user_id: str, assessment_id: Optional[int] = None) -> Optional[Dict]:
    """
    Identify the user's weakest skill based on the lowest accuracy percentage.

    Args:
        cur: The active database cursor.
        user_id: The ID of the user to analyze.
        assessment_id: Optional assessment ID to filter by.

    Returns:
        A dictionary containing the weakest skill's data, or None if no data is available.
    """
    # Reuse the skill performance data-gathering function.
    skill_performance = get_skill_performance_data(user_id, assessment_id)
    if not skill_performance:
        return None

    # Use the `min()` function with a lambda key to find the dictionary
    # with the lowest 'accuracy_percentage' value in the list.
    return min(skill_performance, key=lambda x: x["accuracy_percentage"])


def get_strongest_skill(user_id: str, assessment_id: Optional[int] = None) -> Optional[Dict]:
    """
    Identify the user's strongest skill based on the highest accuracy percentage.

    Args:
        cur: The active database cursor.
        user_id: The ID of the user to analyze.
        assessment_id: Optional assessment ID to filter by.

    Returns:
        A dictionary containing the strongest skill's data, or None if no data is available.
    """
    # Reuse the skill performance data-gathering function.
    skill_performance = get_skill_performance_data(user_id, assessment_id)
    if not skill_performance:
        return None

    # Use the `max()` function with a lambda key to find the dictionary
    # with the highest 'accuracy_percentage' value in the list.
    return max(skill_performance, key=lambda x: x["accuracy_percentage"])


def calculate_improvement_suggestions(user_id: str, assessment_id: Optional[int] = None) -> List[Dict]:
    """
    Generate a list of actionable improvement suggestions based on performance data.

    This function orchestrates calls to other utilities to gather various metrics
    and then applies business logic to create targeted advice.

    Args:
        cur: The active database cursor.
        user_id: The ID of the user to analyze.
        assessment_id: Optional assessment ID to filter by.

    Returns:
        A list of suggestion dictionaries, each with a type, priority, and message.
    """
    suggestions = []

    # --- Phase 1: Gather all necessary performance data ---
    overall_performance = get_overall_performance_summary(user_id, assessment_id)
    skill_performance = get_skill_performance_data(user_id, assessment_id)
    difficulty_performance = get_difficulty_wise_performance(user_id, assessment_id)

    # --- Phase 2: Apply rules to generate suggestions ---

    # Rule 1: Suggestion for low overall accuracy.
    if overall_performance["accuracy_percentage"] < 60:
        suggestions.append(
            {
                "type": "overall_accuracy",
                "priority": "high",
                "message": "Your overall accuracy is below 60%. Focus on understanding fundamental concepts before proceeding.",
            }
        )

    # Rule 2: Suggestions for specific weak skills.
    for skill in skill_performance:
        if skill["accuracy_percentage"] < 50:
            suggestions.append(
                {
                    "type": "skill_weakness",
                    "priority": "high",
                    "skill_name": skill["skill_name"],
                    "message": (
                        f"Your performance in {skill['skill_name']} is {skill['accuracy_percentage']:.1f}%. "
                        "This skill requires immediate attention."
                    ),
                }
            )

    # Rule 3: Suggestions for struggling with a certain difficulty level.
    for difficulty, data in difficulty_performance.items():
        if data["accuracy_percentage"] < 40 and data["total_questions"] > 0:
            suggestions.append(
                {
                    "type": "difficulty_weakness",
                    "priority": "medium",
                    "difficulty": difficulty,
                    "message": (
                        f"You're struggling with {difficulty} questions ({data['accuracy_percentage']:.1f}% accuracy). "
                        "Consider additional practice at this level."
                    ),
                }
            )

    return suggestions
