"""
Database Utility Functions

This module provides reusable database connection and query utilities
to reduce code duplication and improve maintainability.
"""

import json
from contextlib import contextmanager
from functools import wraps
from typing import Any, Dict, List, Optional, Tuple

import psycopg2
import psycopg2.pool
from database.db import get_db_context
from dotenv import load_dotenv

from ..config import config
from ..utils.logger import info as log_info
from ..utils.logger import log_database_error
from .logger import debug, error, warning

load_dotenv()

# Initialize the connection pool
connection_pool = None


def with_db(func):
    """Decorator that provides database context to functions."""

    @wraps(func)
    def wrapper(*args, **kwargs):
        with get_db_context() as db:
            return func(db, *args, **kwargs)

    return wrapper


def _execute_upsert(cur, table: str, data: Dict[str, Any], conflict_columns: List[str], update_columns: List[str]):
    """Builds and executes a generic INSERT...ON CONFLICT query."""
    columns = list(data.keys())
    placeholders = ", ".join(["%s"] * len(columns))
    values = list(data.values())

    query = f"INSERT INTO {table} ({', '.join(columns)}) VALUES ({placeholders})"

    if conflict_columns:
        query += f" ON CONFLICT ({', '.join(conflict_columns)})"
        if update_columns:
            updates = ", ".join([f"{col} = EXCLUDED.{col}" for col in update_columns])
            query += f" DO UPDATE SET {updates}"
        else:
            query += " DO NOTHING"

    cur.execute(query, tuple(values))


def _execute_exists_check(cur, table: str, conditions: Dict[str, Any]) -> bool:
    """Builds and executes a generic 'SELECT 1' query to check for existence."""
    where_clause = " AND ".join([f"{col} = %s" for col in conditions.keys()])
    query = f"SELECT 1 FROM {table} WHERE {where_clause} LIMIT 1"
    cur.execute(query, tuple(conditions.values()))
    return bool(cur.fetchone())


def _execute_count(cur, table: str, conditions: Optional[Dict[str, Any]] = None) -> int:
    """Builds and executes a generic 'SELECT COUNT(*)' query."""
    query = f"SELECT COUNT(*) FROM {table}"
    params = None
    if conditions:
        where_clause = " AND ".join([f"{col} = %s" for col in conditions.keys()])
        query += f" WHERE {where_clause}"
        params = tuple(conditions.values())

    cur.execute(query, params)
    return cur.fetchone()[0]


@contextmanager
def get_db_connection(cursor_factory=None):
    # This function is well-designed and remains unchanged.
    conn = None
    try:
        conn = psycopg2.connect(**config.DATABASE_CONFIG)
        yield conn
    except Exception as e:
        error(f"Database connection error: {e}")
        raise
    finally:
        if conn:
            conn.close()


def insert_or_update(table: str, data: Dict[str, Any], conflict_columns: List[str], update_columns: List[str]) -> bool:
    """Insert a record or update on conflict using a managed transaction."""
    try:
        with get_db_connection() as conn:
            with conn.cursor() as cur:
                # Call the internal query executor
                _execute_upsert(cur, table, data, conflict_columns, update_columns)
            conn.commit()
        return True
    except Exception as e:
        error(f"Insert/Update failed for table {table}: {e}")
        raise


def exists_in_table(table: str, conditions: Dict[str, Any]) -> bool:
    """Check if a record exists using a managed, read-only transaction."""
    try:
        with get_db_connection() as conn:
            with conn.cursor() as cur:
                # Call the internal query executor
                return _execute_exists_check(cur, table, conditions)
    except Exception as e:
        error(f"Exists check failed for table {table}: {e}")
        raise


def get_table_count(table: str, conditions: Optional[Dict[str, Any]] = None) -> int:
    """Get the count of records using a managed, read-only transaction."""
    try:
        with get_db_connection() as conn:
            with conn.cursor() as cur:
                # Call the internal query executor
                return _execute_count(cur, table, conditions)
    except Exception as e:
        error(f"Count failed for table {table}: {e}")
        raise


@contextmanager
def get_db_cursor(cursor_factory=None):
    """
    Context manager for database cursors with automatic cleanup.

    Args:
        cursor_factory: Optional cursor factory (e.g., psycopg2.extras.DictCursor)

    Yields:
        Tuple of (connection, cursor)
    """
    conn = None
    cursor = None
    try:
        conn = psycopg2.connect(**config.DATABASE_CONFIG)
        cursor = conn.cursor(cursor_factory=cursor_factory)
        yield conn, cursor
    except Exception as e:
        if conn:
            conn.rollback()
        error(f"Database cursor error: {e}")
        raise
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()


def execute_query(
    query: str,
    params: Optional[Tuple] = None,
    commit: bool = False,
    fetch_one: bool = False,
    fetch_all: bool = False,
    cursor_factory=None,
):
    """
    Execute a database query with optional fetching.

    Args:
        query: SQL query string
        params: Query parameters
        commit: Whether to commit the transaction
        fetch_one: Whether to fetch one result
        fetch_all: Whether to fetch all results
        cursor_factory: Optional cursor factory

    Returns:
        Query result or None
    """
    try:
        with get_db_cursor(cursor_factory=cursor_factory) as (conn, cursor):
            cursor.execute(query, params)

            if fetch_one:
                result = cursor.fetchone()
            elif fetch_all:
                result = cursor.fetchall()
            else:
                result = None

            if commit:
                conn.commit()

            return result
    except Exception as e:
        error(f"Query execution failed: {e}")
        raise


def get_single_value(query: str, params: Optional[Tuple] = None, default=None):
    """
    Execute a query and return a single value.

    Args:
        query: SQL query string
        params: Query parameters
        default: Default value if no result

    Returns:
        Single value from query result or default
    """
    try:
        result = execute_query(query, params, fetch_one=True)
        return result[0] if result else default
    except Exception as e:
        error(f"Failed to get single value: {e}")
        return default


def execute_transaction(queries: List[Dict[str, Any]], cursor_factory=None) -> bool:
    """
    Execute multiple queries in a single transaction.

    Args:
        queries: List of dictionaries with 'query' and optional 'params' keys
        cursor_factory: Optional cursor factory

    Returns:
        True if all queries executed successfully
    """
    debug(f"Executing transaction with {len(queries)} queries")

    try:
        with get_db_cursor(cursor_factory=cursor_factory) as (conn, cursor):
            for query_info in queries:
                query = query_info["query"]
                params = query_info.get("params")
                cursor.execute(query, params)

            conn.commit()
            debug("Transaction committed successfully")
            return True

    except Exception as e:
        error(f"Transaction failed: {e}")
        raise


def safe_json_loads(json_data: Any, default: Any = None) -> Any:
    """
    Safely parse JSON string with error handling.
    Handles both string and already-parsed objects.

    Args:
        json_data: JSON string to parse or already parsed object
        default: Default value if parsing fails

    Returns:
        Parsed JSON object or default value
    """
    try:
        # If it's None, return default
        if json_data is None:
            return default

        # If it's already a dict/list (parsed object), return it directly
        if isinstance(json_data, (dict, list)):
            return json_data

        # If it's an empty string, return default
        if isinstance(json_data, str) and not json_data.strip():
            return default

        # If it's a string, try to parse it
        if isinstance(json_data, str):
            return json.loads(json_data)

        # For any other type (int, bool, etc.), return default
        warning(f"safe_json_loads received unexpected type: {type(json_data)} with value: {json_data}")
        return default

    except json.JSONDecodeError as e:
        warning(
            f"Failed to parse JSON string: {e} | Value: {json_data[:100] if len(str(json_data)) > 100 else json_data}"
        )
        return default
    except Exception as e:
        warning(f"Unexpected error in safe_json_loads: {e} | Type: {type(json_data)} | Value: {json_data}")
        return default


def safe_json_dumps(obj: Any, default: str = "{}") -> str:
    """
    Safely serialize object to JSON string with error handling.

    Args:
        obj: Object to serialize
        default: Default value if serialization fails

    Returns:
        JSON string or default value
    """
    try:
        return json.dumps(obj)
    except (TypeError, ValueError) as e:
        warning(f"Failed to serialize JSON: {e}")
        return default


async def get_connection_pool():
    """Get or create the database connection pool with validation"""
    global connection_pool
    if connection_pool is None:
        try:
            log_info(
                "Initializing database connection pool",
                min_connections=config.MIN_CONNECTIONS,
                max_connections=config.MAX_CONNECTIONS,
            )

            connection_pool = psycopg2.pool.SimpleConnectionPool(
                config.MIN_CONNECTIONS, config.MAX_CONNECTIONS, **config.DATABASE_CONFIG
            )

            # Test connection
            conn = connection_pool.getconn()
            try:
                with conn.cursor() as cur:
                    cur.execute("SELECT 1")
                log_info("Database connection pool initialized successfully")
            finally:
                connection_pool.putconn(conn)
        except Exception as e:
            log_database_error("initialize_pool", "connection_pool", e)
            raise
    return connection_pool
