"""
Centralized Logging Utility Module

This module establishes a robust, centralized logging system for the application,
designed to replace standard print statements with structured, configurable logging.

Key Features:
- Environment-Based Configuration: Log levels and debug modes are controlled via .env files.
- Multiple Handlers: Logs are simultaneously sent to the console and to log files.
- Structured Logging: Outputs logs in a consistent, parseable format.
- Segregated Log Files: All logs go to 'herbit.log', while errors are isolated in 'error.log' for easy monitoring.
- Context-Aware Logging: A 'ContextFilter' injects contextual data (e.g., request_id) into every log message.
- Hierarchical Loggers: Provides specialized loggers for different application domains (API, DB, Auth).
- Performance Monitoring: Includes utilities for logging the duration of critical operations.
"""

import logging
import os
import sys
from pathlib import Path
from typing import Any, Dict, Optional

from dotenv import load_dotenv

# Load environment variables from a .env file at the start of the module.
load_dotenv()


class ContextFilter(logging.Filter):
    """
    A custom logging filter to inject shared, contextual information into log records.

    This is essential for tracing requests through the application. A middleware can
    set a context (e.g., `request_id`, `user_id`) at the beginning of a request,
    and every log message generated during that request will automatically include it.
    """

    def __init__(self):
        """Initializes the filter with an empty context dictionary."""
        super().__init__()
        self.context: Dict[str, Any] = {}

    def filter(self, record: logging.LogRecord) -> bool:
        """
        This method is automatically called by the logging framework for each log.

        It enriches the log `record` by adding all key-value pairs from the
        current context dictionary as attributes to the record.
        """
        for key, value in self.context.items():
            setattr(record, key, value)
        # Return True to indicate that the record should always be processed.
        return True

    def set_context(self, **kwargs):
        """Sets or updates the context dictionary with new key-value pairs."""
        self.context.update(kwargs)

    def clear_context(self):
        """Clears all information from the context dictionary."""
        self.context.clear()


class HerbitLogger:
    """
    The main class for the Herbit application's logging system.

    This class encapsulates all logging setup and provides a comprehensive interface
    for different types of logging (e.g., general, API, database). It is instantiated
    once to create a single, shared logger for the entire application.
    """

    def __init__(self):
        """
        Initializes the logger, creates the context filter, and runs the main setup routine.
        """
        self.context_filter = ContextFilter()
        self._setup_logging()

    def _setup_logging(self):
        """
        Configures the entire logging system based on environment variables.
        This is the core setup method that wires everything together.
        """
        # Determine the primary log level from environment variables, defaulting to INFO.
        log_level = os.getenv("LOG_LEVEL", "INFO").upper()
        # Check for a separate DEBUG flag, which is a common convention.
        debug_enabled = os.getenv("DEBUG", "false").lower() == "true"

        # If the global DEBUG flag is enabled, it overrides the LOG_LEVEL to DEBUG for maximum verbosity.
        if debug_enabled:
            log_level = "DEBUG"

        # Use pathlib to create the 'logs' directory. `exist_ok=True` prevents errors if it already exists.
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)

        # Get the root logger. Configuring the root logger ensures that all child loggers
        # (e.g., "herbit.api") inherit its settings (level, handlers).
        root_logger = logging.getLogger()
        root_logger.setLevel(getattr(logging, log_level, logging.INFO))
        # Clear any previously attached handlers to prevent duplicate logging if this is re-run.
        root_logger.handlers.clear()

        # --- Define Log Formatters ---
        # A detailed formatter for file logs and debug mode, including function name and line number.
        detailed_formatter = logging.Formatter(
            "%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s"
        )
        # A simpler formatter for production console output to reduce noise.
        simple_formatter = logging.Formatter("%(asctime)s - %(levelname)s - %(message)s")

        # --- Configure Console Handler ---
        # This handler directs log output to the standard output (your terminal).
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(getattr(logging, log_level, logging.INFO))
        # Use the detailed formatter in debug mode, otherwise use the simple one.
        console_handler.setFormatter(simple_formatter if not debug_enabled else detailed_formatter)
        # Attach the context filter to the handler so console logs are enriched.
        console_handler.addFilter(self.context_filter)

        # --- Configure File Handlers ---
        # Handler for 'herbit.log': captures everything from DEBUG level upwards for complete history.
        all_logs_handler = logging.FileHandler(log_dir / "herbit.log")
        all_logs_handler.setLevel(logging.DEBUG)
        all_logs_handler.setFormatter(detailed_formatter)
        all_logs_handler.addFilter(self.context_filter)

        # Handler for 'error.log': captures only ERROR level and above for easy monitoring and alerting.
        error_handler = logging.FileHandler(log_dir / "error.log")
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(detailed_formatter)
        error_handler.addFilter(self.context_filter)

        # Attach all configured handlers to the root logger.
        root_logger.addHandler(console_handler)

        # --- Set up specific, hierarchical loggers for different application modules ---
        # This allows for more granular control and better log organization.
        # General application logger.
        self.logger = logging.getLogger("herbit")
        self.api_logger = logging.getLogger("herbit.api")
        self.db_logger = logging.getLogger("herbit.database")
        self.auth_logger = logging.getLogger("herbit.auth")
        self.quiz_logger = logging.getLogger("herbit.quiz")

        # Reduce noise from verbose third-party libraries.
        self._configure_third_party_loggers()

    def _configure_third_party_loggers(self):
        """Sets higher log levels for chatty third-party libraries to keep logs clean."""
        # Suppress noisy INFO logs from httpx (e.g., "Request: GET ..."), but show warnings.
        logging.getLogger("httpx").setLevel(logging.WARNING)

        # Suppress uvicorn's access logs (e.g., "127.0.0.1:1234 - 'GET / HTTP/1.1' 200")
        # in non-debug environments, as they can be very verbose.
        if os.getenv("LOG_LEVEL", "INFO").upper() != "DEBUG":
            logging.getLogger("uvicorn.access").setLevel(logging.WARNING)

    def set_context(self, **kwargs):
        """Public method to set the logging context via the context filter."""
        self.context_filter.set_context(**kwargs)

    def clear_context(self):
        """Public method to clear the logging context."""
        self.context_filter.clear_context()

    def configure_third_party_logger(self, logger_name: str, level: str = "WARNING"):
        """Allows runtime configuration of a specific third-party logger's level."""
        third_party_logger = logging.getLogger(logger_name)
        third_party_logger.setLevel(getattr(logging, level.upper(), logging.WARNING))

    # --- Standard Logging Methods ---
    def debug(self, message: str, extra: Optional[Dict[str, Any]] = None, **kwargs):
        """Logs a message with level DEBUG."""
        self._log(logging.DEBUG, message, extra, **kwargs)

    def info(self, message: str, extra: Optional[Dict[str, Any]] = None, **kwargs):
        """Logs a message with level INFO."""
        self._log(logging.INFO, message, extra, **kwargs)

    def warning(self, message: str, extra: Optional[Dict[str, Any]] = None, **kwargs):
        """Logs a message with level WARNING."""
        self._log(logging.WARNING, message, extra, **kwargs)

    def warn(self, message: str, extra: Optional[Dict[str, Any]] = None, **kwargs):
        """Alias for the warning method for convenience."""
        self.warning(message, extra, **kwargs)

    def error(
        self, message: str, exception: Optional[Exception] = None, extra: Optional[Dict[str, Any]] = None, **kwargs
    ):
        """
        Logs a message with level ERROR. If an exception is provided,
        it automatically includes the full stack trace in the log.
        """
        if exception:
            # `exc_info=True` tells the logger to capture and format the exception details.
            self.logger.error(
                f"{message} - Exception: {str(exception)}",
                extra=extra,
                exc_info=True,
                **kwargs,
            )
        else:
            self._log(logging.ERROR, message, extra, **kwargs)

    def critical(self, message: str, extra: Optional[Dict[str, Any]] = None, **kwargs):
        """Logs a message with level CRITICAL."""
        self._log(logging.CRITICAL, message, extra, **kwargs)

    def _log(self, level: int, message: str, extra: Optional[Dict[str, Any]] = None, **kwargs):
        """
        Internal helper method to avoid code duplication in the standard logging methods.
        It formats the message with any extra data provided.
        """
        if extra:
            message = f"{message} - Extra: {extra}"
        if kwargs:
            message = f"{message} - Context: {kwargs}"
        self.logger.log(level, message)

    # --- Specialized, Structured Logging Methods ---
    def log_api_request(self, method: str, endpoint: str, **kwargs):
        """Logs an incoming API request in a structured format using the API logger."""
        self.api_logger.info(f"API Request: {method} {endpoint}", extra=kwargs)

    def log_api_response(
        self, method: str, endpoint: str, status_code: int, response_time: Optional[float] = None, **kwargs
    ):
        """Logs an API response, automatically setting the log level based on status code."""
        context = {"response_time_ms": f"{response_time * 1000:.2f}" if response_time else None, **kwargs}
        # Log client/server errors (4xx/5xx) as ERROR, and successful responses as DEBUG.
        level = logging.ERROR if status_code >= 400 else logging.DEBUG
        self.api_logger.log(level, f"API Response: {method} {endpoint} -> {status_code}", extra=context)

    def log_database_operation(self, operation: str, table: str, duration: Optional[float] = None, **kwargs):
        """Logs a database operation in a structured format using the DB logger."""
        context = {"duration_ms": f"{duration * 1000:.2f}" if duration else None, **kwargs}
        self.db_logger.info(f"DB Op: {operation} on {table}", extra=context)

    def log_database_error(self, operation: str, table: str, error: Exception, **kwargs):
        """Logs a database error with full exception info using the DB logger."""
        context = {"error": str(error), **kwargs}
        self.db_logger.error(f"DB Error: {operation} on {table} failed", extra=context, exc_info=True)

    def log_auth_event(self, event: str, success: bool = True, **kwargs):
        """Logs a security or authentication event using the Auth logger."""
        # Log successful auth events as INFO, and failures as WARNING.
        level = logging.INFO if success else logging.WARNING
        self.auth_logger.log(level, f"Auth Event: {event}", extra=kwargs)

    def log_quiz_event(self, event: str, **kwargs):
        """Logs a business-logic event related to quizzes."""
        self.quiz_logger.info(f"Quiz Event: {event}", extra=kwargs)

    def log_performance(self, operation: str, duration: float, **kwargs):
        """Logs a performance metric, escalating to WARNING if the duration is too long."""
        # Flag operations taking more than 5 seconds as a potential performance issue.
        level = logging.WARNING if duration > 5.0 else logging.INFO
        self.logger.log(level, f"Performance: {operation} took {duration:.2f}s", extra=kwargs)


# --- Global Instance and Convenience Functions ---

# Create a single, global instance of the logger. This ensures that the same configured
# logger is used throughout the entire application, maintaining a shared state.
logger = HerbitLogger()


# These convenience functions are provided so that other modules can simply
# call `from logger import info` and use `info("message")` directly,
# rather than needing to import the `logger` object itself.


def debug(message: str, **kwargs):
    """Convenience function to log a debug message."""
    logger.debug(message, **kwargs)


def info(message: str, **kwargs):
    """Convenience function to log an info message."""
    logger.info(message, **kwargs)


def warning(message: str, **kwargs):
    """Convenience function to log a warning message."""
    logger.warning(message, **kwargs)


def warn(message: str, **kwargs):
    """Alias for the warning convenience function."""
    logger.warning(message, **kwargs)


def error(message: str, exception: Optional[Exception] = None, **kwargs):
    """Convenience function to log an error message."""
    logger.error(message, exception=exception, **kwargs)


def critical(message: str, **kwargs):
    """Convenience function to log a critical message."""
    logger.critical(message, **kwargs)


def log_api_request(method: str, endpoint: str, **kwargs):
    """Convenience function to log an API request."""
    logger.log_api_request(method, endpoint, **kwargs)


def log_api_response(method: str, endpoint: str, status_code: int, **kwargs):
    """Convenience function to log an API response."""
    logger.log_api_response(method, endpoint, status_code, **kwargs)


def log_database_operation(operation: str, table: str, **kwargs):
    """Convenience function to log a database operation."""
    logger.log_database_operation(operation, table, **kwargs)


def log_database_error(operation: str, table: str, error: Exception, **kwargs):
    """Convenience function to log a database error."""
    logger.log_database_error(operation, table, error, **kwargs)


def log_auth_event(event: str, **kwargs):
    """Convenience function to log an authentication event."""
    logger.log_auth_event(event, **kwargs)


def log_quiz_event(event: str, **kwargs):
    """Convenience function to log a quiz-related event."""
    logger.log_quiz_event(event, **kwargs)


def log_performance(operation: str, duration: float, **kwargs):
    """Convenience function to log a performance metric."""
    logger.log_performance(operation, duration, **kwargs)


def set_context(**kwargs):
    """Convenience function to set the global logging context."""
    logger.set_context(**kwargs)


def clear_context():
    """Convenience function to clear the global logging context."""
    logger.clear_context()


def configure_third_party_logger(logger_name: str, level: str = "WARNING"):
    """Convenience function to configure a third-party logger."""
    logger.configure_third_party_logger(logger_name, level)
