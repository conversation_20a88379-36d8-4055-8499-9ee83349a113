"""
Handles asynchronous API interactions with an external Ollama-compatible service.

This module provides a client for querying large language models (LLMs) and
retrieving a list of available models. It is built using aiohttp for high-performance,
non-blocking I/O and includes robust error handling, detailed logging, and request tracing.
"""

import asyncio
import json
import time
import uuid

from aiohttp import ClientError, ClientSession, ClientTimeout, ContentTypeError, TraceConfig
from dotenv import load_dotenv

from ..config import config
from ..utils.logger import debug, error, info

# Load environment variables from a .env file, such as BASE_URL and API_KEY.
load_dotenv()


async def get_models():
    """
    Retrieves the list of available models from the Ollama API endpoint.

    Returns:
        A dictionary containing the API response. On success, this includes the
        model data. On failure, it contains a standardized error message.
    """
    # Generate a unique ID for this specific request to trace its lifecycle through the logs.
    request_id = str(uuid.uuid4())[:8]
    # Construct the full URL and headers for the API request from constants.
    url = f"{config.BASE_URL}/api/models"
    headers = {"Authorization": f"Bearer {config.API_KEY}"}

    info(f"[{request_id}] Starting request to fetch models from {url}")

    start_time = time.time()
    # Use an `aiohttp.ClientSession` within an `async with` block to ensure proper
    # handling of the connection pool and resource cleanup.
    async with ClientSession() as session:
        try:
            # Make the asynchronous GET request.
            async with session.get(url, headers=headers) as response:
                elapsed = time.time() - start_time
                info(f"[{request_id}] Response received in {elapsed:.2f}s with status {response.status}")

                # Check for a successful HTTP response (status 200) before processing.
                if response.status == 200:
                    result = await response.json()
                    # Log a preview of the successful response to avoid flooding logs.
                    debug(f"[{request_id}] Successful response: {json.dumps(result)[:200]}...")
                    return result

                # If the response was not successful, log the error and return a structured error message.
                error_text = await response.text()
                error(f"[{request_id}] Error response ({response.status}): {error_text}")
                return {"error": response.status, "message": error_text}

        # Handle network-level errors, such as DNS resolution failure or connection refusal.
        except ClientError as err:
            elapsed = time.time() - start_time
            error(f"[{request_id}] Client error after {elapsed:.2f}s: {str(err)}")
            return {"error": "ClientError", "message": str(err)}


async def query_model(prompt, model_id, timeout=300):
    """
    Sends a prompt to a specified language model and awaits a response.

    Args:
        prompt (str): The input text or question for the model.
        model_id (str): The identifier of the model to be queried.
        timeout (int): The total time in seconds to wait for the entire request to complete.

    Returns:
        A dictionary containing the model's response or a standardized error message.
    """
    request_id = str(uuid.uuid4())[:8]
    url = f"{config.BASE_URL}/api/chat/completions"
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {config.API_KEY}",
    }
    data = {"model": model_id, "messages": [{"role": "user", "content": prompt}]}

    timeout_obj = ClientTimeout(total=timeout)

    prompt_preview = prompt[:50] + "..." if len(prompt) > 50 else prompt
    info(f"[{request_id}] Starting request to {url} for model {model_id}")
    debug(f"[{request_id}] Prompt preview: {prompt_preview}")
    debug(f"[{request_id}] Request timeout: {timeout}s")

    # --- Step 1: Define the TraceConfig and its handlers *before* creating the session ---
    trace_config = TraceConfig()

    async def on_request_start(session, trace_config_ctx, params):
        debug(f"[{request_id}] Trace: Request started")

    async def on_request_end(session, trace_config_ctx, params):
        debug(f"[{request_id}] Trace: Request ended")

    async def on_connection_create_start(session, trace_config_ctx, params):
        debug(f"[{request_id}] Trace: Connection creation started")

    async def on_connection_create_end(session, trace_config_ctx, params):
        debug(f"[{request_id}] Trace: Connection creation ended")

    async def on_dns_resolvehost_start(session, trace_config_ctx, params):
        debug(f"[{request_id}] Trace: DNS resolution started for {params.host}")

    async def on_dns_resolvehost_end(session, trace_config_ctx, params):
        debug(f"[{request_id}] Trace: DNS resolution ended for {params.host}")

    trace_config.on_request_start.append(on_request_start)
    trace_config.on_request_end.append(on_request_end)
    trace_config.on_connection_create_start.append(on_connection_create_start)
    trace_config.on_connection_create_end.append(on_connection_create_end)
    trace_config.on_dns_resolvehost_start.append(on_dns_resolvehost_start)
    trace_config.on_dns_resolvehost_end.append(on_dns_resolvehost_end)

    start_time = time.time()

    # --- Step 2: Pass the trace_configs list to the ClientSession constructor ---
    # The `trace_configs` parameter expects a list of TraceConfig objects.
    async with ClientSession(timeout=timeout_obj, trace_configs=[trace_config]) as session:
        try:
            # --- Step 3: The incorrect line `session._trace_configs.append(...)` has been removed ---

            async with session.post(url, headers=headers, json=data) as response:
                response_time = time.time()
                elapsed = response_time - start_time
                info(f"[{request_id}] Response received in {elapsed:.2f}s with status {response.status}")
                debug(f"[{request_id}] Response headers: {dict(response.headers)}")

                if response.status == 200:
                    response_json = await response.json()
                    parsing_time = time.time() - response_time
                    debug(f"[{request_id}] Response parsed in {parsing_time:.2f}s")
                    info(f"[{request_id}] Successfully received response from model {model_id}")
                    response_preview = str(response_json)[:200] + "..."
                    debug(f"[{request_id}] Response preview: {response_preview}")
                    return response_json

                error_text = await response.text()
                if response.status == 504:
                    error_message = "Gateway timeout: The model service took too long to respond."
                elif response.status == 503:
                    error_message = "Service unavailable: The model service is temporarily down."
                elif response.status == 429:
                    error_message = "Rate limit exceeded: Too many requests sent to the model service."
                elif response.status == 500:
                    error_message = "Internal server error occurred in the model service."
                else:
                    error_message = error_text

                error(f"[{request_id}] Error response ({response.status}): {error_message}")

                try:
                    error_json = await response.json()
                    debug(f"[{request_id}] Parsed error JSON: {error_json}")
                    error_message = error_json.get("error", error_message)
                except (ContentTypeError, json.JSONDecodeError):
                    debug(f"[{request_id}] Could not parse error response as JSON.")

                return {"error": response.status, "message": error_message, "status": "error"}

        except asyncio.TimeoutError:
            elapsed = time.time() - start_time
            error(f"[{request_id}] Request timed out after {elapsed:.2f}s (limit: {timeout}s)")
            return {"error": "Timeout", "message": f"Request timed out after {timeout} seconds", "status": "error"}

        except ClientError as err:
            elapsed = time.time() - start_time
            error(f"[{request_id}] Client error after {elapsed:.2f}s: {str(err)}")
            return {"error": "ClientError", "message": str(err), "status": "error"}

        except Exception as e:
            elapsed = time.time() - start_time
            # exc_info is better for logging
            error(f"[{request_id}] Unexpected error after {elapsed:.2f}s: {str(e)}", exc_info=True)
            return {"error": "UnexpectedError", "message": str(e), "status": "error"}
