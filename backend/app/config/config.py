"""
This module stores constants for the application.
By keeping them in one place, we can easily update them and ensure consistency.
"""

import os

EMAIL_PATTERN = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"

VALID_MODES = ["fixed", "dynamic"]

VALID_LEVELS = ["easy", "intermediate", "advanced"]

SCORE_MAPPING = {"easy": 1, "intermediate": 2, "advanced": 3}

BASE_SALT = "herbit_quiz_system"

MIN_LENGTH = 8

ALPHABET = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890"

ENTITY_TYPE_MAPPING = {
    "assessment": 1,
    "skill": 2,
    "session": 3,
}

REVERSE_ENTITY_TYPE_MAPPING = {v: k for k, v in ENTITY_TYPE_MAPPING.items()}

SESSION_CODE_LENGTH = 6

SALT_HASH_LENGTH = 16

BASE_URL = os.getenv("BASE_URL")

API_KEY = os.getenv("API_KEY")

EASY_COUNT = int(os.getenv("EASY_QUESTIONS_COUNT", "10"))

INTERMEDIATE_COUNT = int(os.getenv("INTERMEDIATE_QUESTIONS_COUNT", "10"))

ADVANCED_COUNT = int(os.getenv("ADVANCED_QUESTIONS_COUNT", "10"))

MIN_CONNECTIONS = int(os.getenv("DB_MIN_CONNECTIONS", "1"))

MAX_CONNECTIONS = int(os.getenv("DB_MAX_CONNECTIONS", "10"))

DATABASE_CONFIG = {
    "user": os.getenv("PG_USER"),
    "password": os.getenv("PG_PASSWORD"),
    "dbname": os.getenv("PG_DATABASE"),
    "host": os.getenv("PG_HOST"),
    "port": int(os.getenv("PG_PORT", "5432")),
}

DATABASE_URL = f"postgresql://{DATABASE_CONFIG['user']}:{DATABASE_CONFIG['password']}@{DATABASE_CONFIG['host']}:{DATABASE_CONFIG['port']}/{DATABASE_CONFIG['dbname']}"

DAPR_BASE_URL = os.getenv("DAPR_ENDPOINT_BACKEND") or os.getenv("DAPR_ENDPOINT")

PUBSUB_NAME = os.getenv("PUBSUB_NAME")

QUESTION_TASKS_TOPIC = os.getenv("QUESTION_TASKS_TOPIC")

DAPR_TASK_URL = f"{DAPR_BASE_URL}/v1.0/publish/{PUBSUB_NAME}/{QUESTION_TASKS_TOPIC}"

ALLOWED_USERS = [user.strip() for user in os.getenv("USERS", "").split(",") if user.strip()]

OAUTH_ERROR_MAP = {
    "invalid_grant": (400, "Your login session has expired. Please try logging in again."),
    "invalid_client": (401, "Authentication is misconfigured. Please contact the system administrator."),
    "invalid_request": (400, "The authentication request was invalid. Please try logging in again."),
    "unauthorized_client": (403, "This application is not authorized. Please contact the system administrator."),
    "unsupported_grant_type": (400, "The authentication method is not supported."),
    "server_error": (503, "The authentication service is temporarily unavailable. Please try again later."),
    "temporarily_unavailable": (503, "The authentication service is temporarily unavailable. Please try again later."),
    "timeout": (504, "The authentication service is not responding. Please try again later."),
    "connection": (504, "Could not connect to the authentication service. Please try again later."),
}

DEX_ISSUER = os.getenv("AUTH_ISSUER", "")

JWKS_URL = os.getenv("AUTH_JWKS_URI", f"{DEX_ISSUER}/keys")
