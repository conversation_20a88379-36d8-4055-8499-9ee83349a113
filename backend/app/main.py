"""
FastAPI application for quiz/assessment management system.
"""

# ===============================
# Standard Library Imports
# ===============================
import asyncio
import os
import time
import uuid
from contextlib import asynccontextmanager

# ===============================
# Third-Party Imports
# ===============================
import psycopg2
import uvicorn
from fastapi import APIRouter, FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, ValidationError
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.middleware.sessions import SessionMiddleware

# ===============================
# Local Imports
# ===============================
from .api.routes.assessment_routes import assessment_router
from .api.routes.auth_routes import setup_auth
from .api.routes.quiz_routes import quiz_router
from .api.routes.report_routes import report_router
from .api.routes.sessions_routes import sessions_router
from .api.routes.skill_routes import skill_router
from .config.config import DATABASE_CONFIG
from .config.env_validator import check_environment
from .models.sessions_manager import periodic_session_expiry
from .utils.api_response import error_response, success_response
from .utils.dapr_util import initialize_dapr_connectivity
from .utils.db_utils import get_connection_pool
from .utils.logger import (
    clear_context,
    error,
    info,
    log_api_request,
    log_api_response,
    set_context,
)

# ===============================
# Constants
# ===============================
ALLOWED_USERS = [user.strip() for user in os.getenv("USERS", "").split(",") if user.strip()]


# ===============================
# Pydantic Models
# ===============================
class UserCheckRequest(BaseModel):
    user_id: str


class TaskResponse(BaseModel):
    task_id: str
    status: str
    message: str


# ===============================
# Application Lifecycle
# ===============================
@asynccontextmanager
async def lifespan(app: FastAPI):
    info("Starting server...")

    if (delay := float(os.getenv("DAPR_STARTUP_DELAY", "0"))) > 0:
        await asyncio.sleep(delay)

    if not check_environment():
        error("Environment validation failed. Server may not function correctly.")
        raise RuntimeError("Environment validation failed.")

    try:
        asyncio.create_task(periodic_session_expiry())
        info("Started periodic session expiry task")
    except Exception as e:
        error(f"Failed to start periodic session expiry task: {str(e)}")

    try:
        await get_connection_pool()
        info("Database connection pool initialized successfully.")
    except Exception as e:
        error(f"Failed to initialize database connection pool: {str(e)}")

    info("Starting Dapr connectivity check in background...")
    asyncio.create_task(initialize_dapr_connectivity())

    info("Application startup completed - service is ready to serve requests")
    yield
    info("Shutting down server...")


# ===============================
# FastAPI App Setup
# ===============================
app = FastAPI(
    title="Herbit API",
    description="API for quiz management and assessment",
    version="1.0.0",
    openapi_version="3.1.0",
    lifespan=lifespan,
    openapi_tags=[
        {"name": "Authentication", "description": "User authentication and authorization"},
        {"name": "Skills", "description": "Skill management and question generation"},
        {"name": "Assessments", "description": "Assessment creation and management"},
        {"name": "Sessions", "description": "Quiz session management"},
        {"name": "Quiz", "description": "Question answering and progress tracking"},
        {"name": "Health", "description": "System health and monitoring"},
    ],
)

api_router = APIRouter(prefix="/api")


# ===============================
# Middleware
# ===============================
class LoggingMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        request_id = str(uuid.uuid4())
        start_time = time.time()
        user_id = None
        try:
            if hasattr(request, "session") and "session" in request.scope:
                user_info = request.session.get("user", {})
                user_id = user_info.get("sub") or user_info.get("email")
        except Exception:
            pass

        log_api_request(
            method=request.method,
            endpoint=str(request.url.path),
            user_id=user_id,
            request_id=request_id,
            query_params=dict(request.query_params) if request.query_params else None,
        )

        set_context(request_id=request_id, user_id=user_id)

        try:
            response = await call_next(request)
            response_time = time.time() - start_time
            log_api_response(
                method=request.method,
                endpoint=str(request.url.path),
                status_code=response.status_code,
                response_time=response_time,
                user_id=user_id,
                request_id=request_id,
            )
            return response
        except Exception as e:
            response_time = time.time() - start_time
            log_api_response(
                method=request.method,
                endpoint=str(request.url.path),
                status_code=500,
                response_time=response_time,
                user_id=user_id,
                request_id=request_id,
                error=str(e),
            )
            error("Unhandled exception in API endpoint", exception=e)
            raise
        finally:
            clear_context()


app.add_middleware(LoggingMiddleware)
app.add_middleware(
    SessionMiddleware,
    secret_key=os.getenv("AUTH_CLIENT_SECRET"),
)
app.add_middleware(
    CORSMiddleware,
    allow_origins=[os.getenv("FRONTEND_URL")] if os.getenv("FRONTEND_URL") else ["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# ===============================
# Exception Handling
# ===============================
@app.exception_handler(ValidationError)
async def validation_exception_handler(_: Request, exc: ValidationError):
    """Handle Pydantic validation errors with detailed error messages."""
    return JSONResponse(
        status_code=400,
        content={
            "detail": "Invalid request format",
            "errors": exc.errors(),
            "message": "Please check your request parameters and try again.",
        },
    )


# ===============================
# Routes
# ===============================
setup_auth(api_router)
info("Authentication setup complete")


@api_router.get("/health")
async def health_check():
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                cur.execute("SELECT 1")
                result = cur.fetchone()

        if result and result[0] == 1:
            return success_response(
                data={"status": "healthy", "database": "connected"},
                message="Health check passed",
            )
        else:
            error("Health check: DB query did not return 1")
            return JSONResponse(
                status_code=503,
                content=error_response(
                    message="Health check failed",
                    code=503,
                    error_type="ServiceUnavailable",
                    details={"database": "error", "detail": "DB query failed"},
                ),
            )
    except Exception as e:
        error("Health check failed", exception=e)
        return JSONResponse(
            status_code=503,
            content=error_response(
                message="Health check failed",
                code=503,
                error_type="ServiceUnavailable",
                details={"error": str(e)},
            ),
        )


# ============================================================================
# WORKER OPERATIONS
# ============================================================================
# Worker service now only handles Dapr pubsub messages internally.
# All worker-related HTTP endpoints have been removed as they are not needed.
# The worker service uses Dapr for all operational data and only touches
# PostgreSQL for storing actual questions (business data).


# Include routers in the API router
api_router.include_router(skill_router, tags=["Skills"])

# Include assessment router in the API router
api_router.include_router(assessment_router, tags=["Assesment"])

# Include report router in the API router
api_router.include_router(report_router)

# Include session router in the API router
api_router.include_router(sessions_router)

# Include quiz router in the API router
api_router.include_router(quiz_router)

# Include API router with all endpoints - this must be done after all routes are defined
app.include_router(api_router)
info("API router setup complete - all routes registered")


# ===============================
# Main Entrypoint
# ===============================
if __name__ == "__main__":
    port = int(os.getenv("SERVER_PORT", "8000"))
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=port,
        access_log=False,
        log_config=None,
    )
