FROM python:3.13-slim

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PYTHONPATH=/app

# Create non-root user
RUN groupadd -r appuser && useradd -r -g appuser appuser

# Install OS dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    libpq-dev build-essential jq shc curl postgresql-client && \
    apt-get clean && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Install Python dependencies
COPY requirements.txt .
RUN pip install --upgrade pip && pip install -r requirements.txt

# Copy code
COPY . .

# Fix permissions for non-root user
RUN chown -R appuser:appuser /app

# Switch to non-root user
USER appuser

# Expose FastAPI port
EXPOSE 8000

# Start command
CMD ["sh", "-c", "\
    set -e; \
    python migrate.py upgrade; \
    echo 'Migrations complete. Starting server...'; \
    if [ \"$DEBUG\" = \"true\" ]; then \
        echo 'Starting in debug mode...'; \
        exec python -m debugpy --listen 0.0.0.0:5678 -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload; \
    else \
        exec uvicorn app.main:app --host 0.0.0.0 --port 8000 --workers 4 --no-access-log; \
    fi \
"]
