<template>
  <PhantomLayout
    :title="assessment ? assessment.name : 'Assessment Details'"
    :description="
      assessment
        ? 'View comprehensive assessment information'
        : 'Loading assessment details...'
    "
  >
    <!-- Loading indicator -->
    <div v-if="isLoading" class="flex justify-center items-center py-12">
      <div
        class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-phantom-blue"
      />
      <span class="ml-4 text-white text-lg">Loading assessment details...</span>
    </div>

    <!-- Message display with animation -->
    <transition name="fade-transition">
      <div v-if="message" class="mb-6 px-6">
        <div
          :class="
            isSuccess
              ? 'bg-green-500/10 border-green-500/30'
              : 'bg-red-500/10 border-red-500/30'
          "
          class="px-6 py-4 rounded-xl border backdrop-blur-sm text-white flex items-center"
        >
          <div v-if="isSuccess" class="flex-shrink-0 mr-3">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5 text-green-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M5 13l4 4L19 7"
              />
            </svg>
          </div>
          <div v-else class="flex-shrink-0 mr-3">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5 text-red-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
              />
            </svg>
          </div>
          <div class="flex-grow">
            {{ message }}
          </div>
          <button
            class="flex-shrink-0 ml-2 p-1 rounded-full hover:bg-white/10 transition-colors"
            @click="clearMessage()"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>
      </div>
    </transition>

    <!-- Assessment Details -->
    <div v-if="assessment && !isLoading" class="w-full max-w-none mx-auto px-8">
      <!-- Header with Back Button -->
      <div class="flex justify-end items-center mb-8">
        <button
          class="btn-phantom-secondary px-5 py-2.5"
          @click="navigateToListAssessments"
        >
          <span class="flex items-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5 mr-2"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M10 19l-7-7m0 0l7-7m-7 7h18"
              />
            </svg>
            Back to Assessments
          </span>
        </button>
      </div>

      <!-- Two Column Layout -->
      <div class="grid grid-cols-1 xl:grid-cols-2 gap-12">
        <!-- LEFT SIDE -->
        <div class="space-y-10">
          <!-- Assessment Basic Information -->
          <section
            class="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-10"
          >
            <h2
              class="text-xl font-semibold text-white mb-6 bg-gradient-to-r from-phantom-blue to-phantom-indigo bg-clip-text text-transparent"
            >
              Assessment Information
            </h2>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
              <div
                class="bg-white/5 backdrop-blur-sm p-6 rounded-xl border border-white/10"
              >
                <p class="text-white/60 text-sm mb-3">Assessment ID</p>
                <p class="text-white font-medium text-lg">
                  {{ decodedAssessmentId || "Loading..." }}
                </p>
              </div>
              <div
                class="bg-white/5 backdrop-blur-sm p-6 rounded-xl border border-white/10"
              >
                <p class="text-white/60 text-sm mb-3">Mode</p>
                <span
                  class="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium"
                  :class="
                    assessment.question_selection_mode === 'fixed'
                      ? 'bg-phantom-indigo/20 text-phantom-indigo border border-phantom-indigo/30'
                      : 'bg-phantom-blue/20 text-phantom-blue border border-phantom-blue/30'
                  "
                >
                  {{
                    assessment.question_selection_mode === "fixed"
                      ? "Fixed"
                      : "Dynamic"
                  }}
                </span>
              </div>
              <div
                class="bg-white/5 backdrop-blur-sm p-6 rounded-xl border border-white/10"
              >
                <p class="text-white/60 text-sm mb-3">Total Questions</p>
                <p class="text-white font-medium text-lg">
                  {{ assessment.total_questions || "N/A" }}
                </p>
              </div>
              <div
                class="bg-white/5 backdrop-blur-sm p-6 rounded-xl border border-white/10"
              >
                <p class="text-white/60 text-sm mb-3">Duration</p>
                <p class="text-white font-medium text-lg">
                  {{
                    assessment.duration_minutes
                      ? `${assessment.duration_minutes} min`
                      : "N/A"
                  }}
                </p>
              </div>
            </div>

            <!-- Description Section -->
            <div v-if="assessment.description">
              <h3 class="text-white text-lg font-medium mb-6">Description</h3>
              <div
                class="bg-white/5 backdrop-blur-sm rounded-xl p-8 max-h-[400px] overflow-y-auto custom-scrollbar-thin border border-white/10"
              >
                <div class="text-white/90 leading-relaxed">
                  <p
                    v-for="(paragraph, index) in formattedDescription"
                    :key="index"
                    class="mb-3"
                  >
                    {{ paragraph }}
                  </p>
                </div>
              </div>
            </div>
          </section>

          <!-- Skills Section (Collapsible) -->
          <section
            v-if="assessment.skills && assessment.skills.length > 0"
            class="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-10"
          >
            <div
              class="flex justify-between items-center mb-6 cursor-pointer"
              @click="toggleSkillsSection"
            >
              <h2
                class="text-xl font-semibold text-white bg-gradient-to-r from-phantom-blue to-phantom-indigo bg-clip-text text-transparent"
              >
                Associated Skills ({{ assessment.skills.length }})
              </h2>
              <button class="text-white/60 hover:text-white transition-colors">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5 transform transition-transform duration-200"
                  :class="{ 'rotate-180': showSkills }"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M19 9l-7 7-7-7"
                  />
                </svg>
              </button>
            </div>

            <transition name="slide-down">
              <div v-if="showSkills" class="space-y-4">
                <div
                  v-for="skill in assessment.skills"
                  :key="skill.id"
                  class="bg-white/5 backdrop-blur-sm p-4 rounded-xl border border-white/10 hover:bg-white/10 transition-colors cursor-pointer"
                  @click="navigateToSkillDetail(skill)"
                >
                  <h3 class="text-white font-medium flex items-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-4 w-4 mr-3 text-phantom-blue"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"
                      />
                    </svg>
                    {{ skill.name }}
                  </h3>
                </div>
              </div>
            </transition>
          </section>
        </div>

        <!-- RIGHT SIDE -->
        <div class="space-y-10">
          <!-- Questions Statistics Section -->
          <section
            v-if="assessment.question_stats"
            class="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-10"
          >
            <h2
              class="text-xl font-semibold text-white mb-6 bg-gradient-to-r from-phantom-blue to-phantom-indigo bg-clip-text text-transparent"
            >
              Questions Statistics
            </h2>

            <!-- Two-column layout for question stats -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <!-- LEFT SIDE: Available Questions -->
              <div
                class="bg-white/5 backdrop-blur-sm p-8 rounded-xl border border-white/10"
              >
                <h3
                  class="text-phantom-blue font-medium mb-6 flex items-center"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5 mr-3"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
                    />
                  </svg>
                  Available Question Pool
                </h3>
                <div class="space-y-4">
                  <div class="flex justify-between items-center">
                    <span class="text-white/60 flex items-center">
                      <div class="w-3 h-3 bg-green-400 rounded-full mr-3" />
                      Easy:
                    </span>
                    <span class="text-green-400 font-medium text-lg">{{
                      assessment.question_stats.available.easy
                    }}</span>
                  </div>
                  <div class="flex justify-between items-center">
                    <span class="text-white/60 flex items-center">
                      <div class="w-3 h-3 bg-yellow-400 rounded-full mr-3" />
                      Intermediate:
                    </span>
                    <span class="text-yellow-400 font-medium text-lg">{{
                      assessment.question_stats.available.intermediate
                    }}</span>
                  </div>
                  <div class="flex justify-between items-center">
                    <span class="text-white/60 flex items-center">
                      <div class="w-3 h-3 bg-orange-400 rounded-full mr-3" />
                      Advanced:
                    </span>
                    <span class="text-orange-400 font-medium text-lg">{{
                      assessment.question_stats.available.advanced
                    }}</span>
                  </div>
                  <div class="border-t border-white/10 pt-4">
                    <div class="flex justify-between items-center">
                      <span class="text-white font-medium text-lg"
                        >Total Available:</span
                      >
                      <span class="text-white font-bold text-xl">{{
                        assessment.question_stats.available.total
                      }}</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- RIGHT SIDE: Selected Questions (Only for fixed assessments) -->
              <div
                v-if="assessment.question_selection_mode === 'fixed'"
                class="bg-white/5 backdrop-blur-sm p-8 rounded-xl border border-white/10"
              >
                <h3
                  class="text-phantom-indigo font-medium mb-6 flex items-center"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5 mr-3"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                  Selected Questions
                </h3>
                <div class="space-y-4">
                  <div class="flex justify-between items-center">
                    <span class="text-white/60 flex items-center">
                      <div class="w-3 h-3 bg-green-400 rounded-full mr-3" />
                      Easy:
                    </span>
                    <span class="text-green-400 font-medium text-lg">{{
                      assessment.question_stats.selected.easy
                    }}</span>
                  </div>
                  <div class="flex justify-between items-center">
                    <span class="text-white/60 flex items-center">
                      <div class="w-3 h-3 bg-yellow-400 rounded-full mr-3" />
                      Intermediate:
                    </span>
                    <span class="text-yellow-400 font-medium text-lg">{{
                      assessment.question_stats.selected.intermediate
                    }}</span>
                  </div>
                  <div class="flex justify-between items-center">
                    <span class="text-white/60 flex items-center">
                      <div class="w-3 h-3 bg-orange-400 rounded-full mr-3" />
                      Advanced:
                    </span>
                    <span class="text-orange-400 font-medium text-lg">{{
                      assessment.question_stats.selected.advanced
                    }}</span>
                  </div>
                  <div class="border-t border-white/10 pt-4">
                    <div class="flex justify-between items-center">
                      <span class="text-white font-medium text-lg"
                        >Total Selected:</span
                      >
                      <span class="text-white font-bold text-xl">{{
                        assessment.question_stats.selected.total
                      }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>

          <!-- All Questions Section -->
          <section
            v-if="
              assessment.selected_questions &&
              assessment.selected_questions.length > 0
            "
            class="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-10"
          >
            <div
              class="flex justify-between items-center mb-6 cursor-pointer"
              @click="toggleQuestionsSection"
            >
              <h2
                class="text-xl font-semibold text-white bg-gradient-to-r from-phantom-blue to-phantom-indigo bg-clip-text text-transparent"
              >
                {{
                  assessment.question_selection_mode === "fixed"
                    ? "Selected Questions"
                    : "Available Questions"
                }}
                ({{ assessment.selected_questions.length }})
              </h2>
              <button class="text-white/60 hover:text-white transition-colors">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5 transform transition-transform duration-200"
                  :class="{ 'rotate-180': showQuestions }"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M19 9l-7 7-7-7"
                  />
                </svg>
              </button>
            </div>

            <transition name="slide-down">
              <div
                v-if="showQuestions"
                class="max-h-[600px] overflow-y-auto custom-scrollbar-thin pr-4"
              >
                <div class="space-y-6">
                  <div
                    v-for="(question, index) in sortedSelectedQuestions"
                    :key="question.que_id"
                    class="bg-white/5 backdrop-blur-sm p-6 rounded-xl border border-white/10 hover:bg-white/10 transition-colors"
                    :class="{
                      'border-l-4 border-l-green-400/70':
                        question.level === 'easy',
                      'border-l-4 border-l-yellow-400/70':
                        question.level === 'intermediate',
                      'border-l-4 border-l-orange-400/70':
                        question.level === 'advanced',
                    }"
                  >
                    <div class="flex items-start justify-between mb-4">
                      <div class="flex items-center space-x-3">
                        <span
                          class="bg-white/10 text-white text-xs font-medium px-3 py-1 rounded-full"
                          >{{ index + 1 }}</span
                        >
                        <span class="text-phantom-blue text-sm font-medium">{{
                          question.skill_name
                        }}</span>
                        <span
                          class="text-xs px-2 py-1 rounded-full"
                          :class="{
                            'bg-green-500/20 text-green-400':
                              question.level === 'easy',
                            'bg-yellow-500/20 text-yellow-400':
                              question.level === 'intermediate',
                            'bg-orange-500/20 text-orange-400':
                              question.level === 'advanced',
                          }"
                        >
                          {{ question.level }}
                        </span>
                      </div>
                    </div>

                    <!-- Question Text -->
                    <h3
                      class="text-white text-base font-medium mb-4 leading-relaxed"
                    >
                      {{ question.question }}
                    </h3>

                    <!-- Options -->
                    <div class="space-y-2 mb-4">
                      <div
                        v-for="(option, key) in question.options"
                        :key="key"
                        class="flex items-start space-x-3 p-3 rounded-lg"
                        :class="{
                          'bg-green-500/10 border border-green-500/30':
                            key.toLowerCase() === question.answer.toLowerCase(),
                          'bg-white/5 border border-white/10':
                            key.toLowerCase() !== question.answer.toLowerCase(),
                        }"
                      >
                        <span
                          class="text-white/80 font-medium text-sm min-w-[20px]"
                          >{{ key.toUpperCase() }}.</span
                        >
                        <span class="text-white/90 text-sm leading-relaxed">{{
                          option
                        }}</span>
                        <svg
                          v-if="
                            key.toLowerCase() === question.answer.toLowerCase()
                          "
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-4 w-4 text-green-400 flex-shrink-0 mt-0.5"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M5 13l4 4L19 7"
                          />
                        </svg>
                      </div>
                    </div>

                    <!-- Answer Info -->
                    <div class="flex justify-between items-center text-sm">
                      <span class="text-white/60">
                        {{ Object.keys(question.options).length }} options
                      </span>
                      <span class="text-green-400 font-medium">
                        Correct Answer: {{ question.answer.toUpperCase() }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </transition>
          </section>

          <!-- Sessions Section -->
          <section
            class="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-10"
          >
            <div class="flex justify-between items-center mb-6">
              <h2
                class="text-xl font-semibold text-white bg-gradient-to-r from-phantom-blue to-phantom-indigo bg-clip-text text-transparent"
              >
                Assessment Sessions ({{ assessmentSessions.length }})
              </h2>
              <button
                :disabled="isLoadingSessions"
                class="btn-phantom-secondary px-4 py-2 text-sm"
                @click="refreshSessions"
              >
                <span class="flex items-center">
                  <svg
                    v-if="isLoadingSessions"
                    class="animate-spin -ml-1 mr-2 h-4 w-4"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      class="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      stroke-width="4"
                    />
                    <path
                      class="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    />
                  </svg>
                  <svg
                    v-else
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-4 w-4 mr-2"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                    />
                  </svg>
                  {{ isLoadingSessions ? "Loading..." : "Refresh" }}
                </span>
              </button>
            </div>

            <!-- Sessions List -->
            <div
              v-if="assessmentSessions.length === 0"
              class="text-center py-12"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-16 w-16 mx-auto text-white/20 mb-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                />
              </svg>
              <p class="text-white/60">No sessions found for this assessment</p>
            </div>

            <div v-else class="space-y-6">
              <div
                v-for="session in assessmentSessions"
                :key="session.id"
                class="bg-white/5 backdrop-blur-sm p-6 rounded-xl border border-white/10 hover:bg-white/10 transition-colors cursor-pointer"
                @click="navigateToSessionDetail(session)"
              >
                <div class="flex justify-between items-start mb-3">
                  <div>
                    <h3 class="text-white font-medium text-lg">
                      {{ session.username }}
                    </h3>
                    <p class="text-white/60 text-sm font-mono">
                      {{ session.session_code }}
                    </p>
                  </div>
                  <span
                    class="inline-flex items-center px-3 py-1.5 rounded-full text-xs font-medium"
                    :class="getSessionStatusClass(session.status)"
                  >
                    {{ session.status || "Pending" }}
                  </span>
                </div>
                <div class="flex justify-between items-center">
                  <span class="text-white/60 text-sm">
                    {{ formatDate(session.created_at) }}
                  </span>
                  <span
                    v-if="session.status === 'completed'"
                    class="text-white/80 font-medium"
                  >
                    {{ session.score || 0 }}/{{ session.total_questions || 0 }}
                    <span class="text-xs text-white/60 ml-2">
                      ({{
                        session.total_questions
                          ? Math.round(
                              (session.score / session.total_questions) * 100,
                            )
                          : 0
                      }}%)
                    </span>
                  </span>
                  <span v-else class="text-white/40">-</span>
                </div>
              </div>
            </div>
          </section>
        </div>
      </div>
    </div>
  </PhantomLayout>
</template>

<script setup>
import { ref, onMounted, computed } from "vue";
import { useRoute, useRouter } from "vue-router";
import { api } from "@/services/api";
import { logError } from "@/utils/errorHandling";
import { useMessageHandler } from "@/utils/messageHandler";
import { useLoadingState } from "@/composables";
import {
  getSkillHashId,
  getSessionHashId,
  getDecodedAssessmentId,
} from "@/utils/hashIds";
import {
  extractResponseData,
  extractErrorInfo,
} from "@/utils/apiResponseHandler";
import { warning } from "@/utils/logger";
import PhantomLayout from "@/components/layout/Layout.vue";

const route = useRoute();
const router = useRouter();

// Composables
const loadingState = useLoadingState(true);
const sessionsLoadingState = useLoadingState();

// Message handling
const { message, isSuccess, setErrorMessage, clearMessage } =
  useMessageHandler();

const assessmentId = ref(null);
const assessment = ref(null);
const assessmentSessions = ref([]);

// Aliases for backward compatibility
const isLoading = loadingState.isLoading;
const isLoadingSessions = sessionsLoadingState.isLoading;

// Toggle states for collapsible sections
const showSkills = ref(false);
const showQuestions = ref(false);

// Decoded assessment ID for display
const decodedAssessmentId = ref(null);

// Format the assessment description into paragraphs
const formattedDescription = computed(() => {
  if (!assessment.value || !assessment.value.description) return [];

  return assessment.value.description
    .split("\n")
    .map((line) => line.trim())
    .filter((line) => line.length > 0);
});

const formatDate = (dateString) => {
  if (!dateString) return "N/A";
  return new Date(dateString).toLocaleString();
};

const getSessionStatusClass = (status) => {
  switch (status) {
    case "completed":
      return "bg-green-500/20 text-green-400 border border-green-500/30";
    case "in_progress":
      return "bg-yellow-500/20 text-yellow-400 border border-yellow-500/30";
    case "pending":
    default:
      return "bg-gray-500/20 text-gray-400 border border-gray-500/30";
  }
};

// Navigation functions
const navigateToListAssessments = () => {
  router.push("/list-assessments");
};

const navigateToSkillDetail = (skill) => {
  const hashId = getSkillHashId(skill);
  router.push(`/skill/${hashId}`);
};

const navigateToSessionDetail = (session) => {
  const hashId = getSessionHashId(session);
  router.push(`/session/${hashId}`);
};

// Toggle functions for collapsible sections
const toggleSkillsSection = () => {
  showSkills.value = !showSkills.value;
};

const toggleQuestionsSection = () => {
  showQuestions.value = !showQuestions.value;
};

// Fetch assessment details
const fetchAssessmentDetails = async () => {
  isLoading.value = true;
  clearMessage();
  assessmentId.value = route.params.id;

  try {
    // Validate assessment ID
    if (!assessmentId.value) {
      throw new Error("Assessment ID is missing");
    }

    // Fetch assessment details
    const response = await api.admin.getAssessment(assessmentId.value);
    const data = extractResponseData(response);

    if (!data) {
      setErrorMessage(
        `Assessment with ID ${assessmentId.value} not found. Please check the URL or return to the assessments list.`,
      );
      isLoading.value = false;
      return;
    }

    assessment.value = data;

    // Decode the assessment ID for display
    try {
      decodedAssessmentId.value = await getDecodedAssessmentId(data);
    } catch (error) {
      warning("Failed to decode assessment ID", { error });
      decodedAssessmentId.value = "N/A";
    }

    // Fetch sessions for this assessment
    await fetchAssessmentSessions();
  } catch (error) {
    logError(error, "fetchAssessmentDetails");
    const errorInfo = extractErrorInfo(error);
    setErrorMessage(errorInfo.message || "Failed to load assessment details");
  } finally {
    isLoading.value = false;
  }
};

// Fetch sessions for this assessment
const fetchAssessmentSessions = async () => {
  isLoadingSessions.value = true;

  try {
    // Get all sessions since backend filtering doesn't seem to work properly
    const params = {
      status_filter: "all", // Get all sessions regardless of status
      limit: 100, // Get more sessions to ensure we don't miss any
    };

    const response = await api.admin.getSessions(params);
    const data = extractResponseData(response);

    let allSessions = [];

    // Handle different response formats
    if (data) {
      if (Array.isArray(data)) {
        allSessions = data;
      } else if (data.sessions) {
        allSessions = data.sessions;
      } else if (data.data) {
        allSessions = Array.isArray(data.data) ? data.data : [];
      }
    }

    // Filter sessions for this specific assessment on frontend
    assessmentSessions.value = allSessions.filter(
      (session) => session.assessment_id_hash === assessmentId.value,
    );
  } catch (error) {
    logError(error, "fetchAssessmentSessions");
    // Don't show error for sessions, just log it
    const errorInfo = extractErrorInfo(error);
    warning("Failed to load sessions for assessment", {
      error: errorInfo.message,
    });
  } finally {
    isLoadingSessions.value = false;
  }
};

// Refresh sessions
const refreshSessions = async () => {
  await fetchAssessmentSessions();
};

// Load assessment details when component mounts
onMounted(() => {
  fetchAssessmentDetails();
});
</script>
