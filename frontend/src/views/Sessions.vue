<template>
  <PhantomLayout title="Generate Session Codes">
    <!-- Main Content -->
    <div class="w-full max-w-3xl mx-auto p-6">
      <section>
        <div class="flex justify-end mb-6">
          <button
            class="btn-phantom-secondary px-4 py-2 text-sm"
            @click="navigateTo('/sessions')"
          >
            <span class="flex items-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4 mr-2"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M10 19l-7-7m0 0l7-7m-7 7h18"
                />
              </svg>
              Back to Sessions
            </span>
          </button>
        </div>
        <form class="space-y-6" @submit.prevent="generateSessions">
          <!-- Assessment Selection -->
          <div>
            <label
              for="assessmentSelect"
              class="block text-white font-medium mb-2"
              >Select Assessment</label
            >
            <select
              id="assessmentSelect"
              v-model="selectedAssessmentId"
              name="assessmentSelect"
              autocomplete="off"
              class="w-full bg-gray-800 border border-white/10 rounded-lg px-4 py-2.5 text-white focus:outline-none focus:ring-2 focus:ring-phantom-blue/50 focus:border-phantom-blue/50 [&>option]:bg-gray-800 [&>option]:text-white"
              required
            >
              <option value="" disabled>Select an assessment</option>
              <option
                v-for="assessment in assessments"
                :key="assessment.id_hash || assessment.id"
                :value="assessment.id_hash || assessment.id"
              >
                {{ assessment.name }}
              </option>
            </select>
          </div>

          <!-- Usernames or Emails -->
          <div>
            <label for="usernames" class="block text-white font-medium mb-2"
              >Usernames or Emails (comma-separated)</label
            >
            <textarea
              id="usernames"
              v-model="usernames"
              name="usernames"
              autocomplete="off"
              class="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2.5 text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-phantom-blue/50 focus:border-phantom-blue/50 h-24"
              placeholder="e.g. user1,user2,user3 or <EMAIL>,<EMAIL>"
              required
            />
            <p class="text-xs text-white/60 mt-1">
              Enter usernames or email addresses separated by commas (no
              spaces). For emails, the username part (before @) will be used as
              the display name.
            </p>
          </div>

          <!-- Loading indicator -->
          <div v-if="isLoading" class="flex justify-center items-center py-4">
            <div
              class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-phantom-blue"
            />
            <span class="ml-3 text-white">Generating session codes...</span>
          </div>

          <!-- Error message only -->
          <div v-if="message && !isSuccess" class="my-4">
            <div class="border-l-4 border-red-500 pl-4 py-3 text-red-300">
              {{ message }}
            </div>
          </div>

          <!-- Results table -->
          <div v-if="sessions.length > 0" class="space-y-4">
            <!-- Sessions table -->
            <div class="overflow-x-auto">
              <table class="w-full text-left border-collapse">
                <thead>
                  <tr class="border-b border-white/10">
                    <th class="py-3 px-4 text-white/80 font-medium">
                      Username
                    </th>
                    <th class="py-3 px-4 text-white/80 font-medium">
                      Session Code
                    </th>
                    <th class="py-3 px-4 text-white/80 font-medium">
                      Session ID
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    v-for="(session, index) in sessions"
                    :key="index"
                    class="border-b border-white/5 hover:bg-white/5 transition-colors"
                  >
                    <td class="py-3 px-4 text-white">
                      {{ session.username }}
                    </td>
                    <td class="py-3 px-4">
                      <span
                        class="font-mono bg-phantom-blue/10 text-phantom-blue px-3 py-1 rounded"
                        >{{ getDisplaySessionCode(session) }}</span
                      >
                    </td>
                    <td class="py-3 px-4 text-white/80">
                      {{ session.sessionDbId }}
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>

            <!-- Generated Link Display -->
            <div
              v-if="generatedLink"
              class="mt-8 pt-8 border-t border-white/10"
            >
              <h3
                class="text-lg font-semibold text-white mb-4 flex items-center"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5 mr-2 text-phantom-blue"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"
                  />
                </svg>
                Generated Quiz Link
              </h3>

              <div
                class="mb-4 bg-phantom-blue/5 p-4 rounded border-l-4 border-phantom-blue/30"
              >
                <p class="text-white/80 text-sm mb-2">
                  Share this link with users to take the quiz:
                </p>
                <div class="flex items-center space-x-2">
                  <input
                    type="text"
                    :value="generatedLink"
                    readonly
                    class="flex-1 px-3 py-2 bg-white/5 rounded text-white text-sm focus:outline-none"
                  />
                  <button
                    class="flex-shrink-0 text-xs text-white/80 bg-phantom-blue/10 hover:bg-phantom-blue/20 rounded p-2 transition-colors"
                    @click="copyToClipboard"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-4 w-4"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"
                      />
                    </svg>
                  </button>
                </div>
              </div>

              <div class="text-sm text-white/70">
                <p>
                  <strong class="text-white/90">Assessment:</strong>
                  {{ selectedAssessmentName }}
                </p>
                <p>
                  <strong class="text-white/90">Generated Sessions:</strong>
                  {{ sessions.length }}
                </p>
                <p>
                  <strong class="text-white/90">Generated:</strong>
                  {{ new Date().toLocaleString() }}
                </p>
              </div>
            </div>
          </div>

          <!-- Submit button (hidden after sessions are created) -->
          <div v-if="sessions.length === 0" class="flex justify-between">
            <button
              type="submit"
              :disabled="isLoading"
              class="btn-phantom px-6 py-3"
            >
              <span class="flex items-center">
                <svg
                  v-if="isLoading"
                  class="animate-spin -ml-1 mr-2 h-5 w-5 text-white"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    class="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    stroke-width="4"
                  />
                  <path
                    class="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  />
                </svg>
                <svg
                  v-else
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5 mr-2"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1 1 21 9z"
                  />
                </svg>
                {{ isLoading ? "Generating..." : "Generate Codes" }}
              </span>
            </button>
          </div>
        </form>
      </section>
    </div>
  </PhantomLayout>
</template>

<script setup>
import { ref, onMounted, computed } from "vue";
import { api } from "@/services/api";
import { getErrorMessage, logError } from "@/utils/errorHandling";
import { useMessageHandler } from "@/utils/messageHandler";
import { extractResponseData } from "@/utils/apiResponseHandler";
import { useLoadingState, useNavigation } from "@/composables";
import globalNotification from "@/composables/useNotification";
import {
  getDisplaySessionCode,
  decodeAssessmentId,
  isHashId,
  decodeSessionCodes,
} from "@/utils/hashIds";
import PhantomLayout from "@/components/layout/Layout.vue";

//----------------------------------------------------------------
// Dependencies & Navigation
//----------------------------------------------------------------
const { message, isSuccess, setErrorMessage, setSuccessMessage, clearMessage } =
  useMessageHandler();
const { showSuccess } = globalNotification;

//----------------------------------------------------------------
// Component State
//----------------------------------------------------------------
const selectedAssessmentId = ref("");
const usernames = ref("");
const assessments = ref([]);
const sessions = ref([]);
const generatedLink = ref(""); // To store the generated quiz link for the assessment.

// Composables
const loadingState = useLoadingState();
const navigation = useNavigation();

// Aliases for backward compatibility
const isLoading = loadingState.isLoading;
const navigateTo = navigation.navigateTo;

//----------------------------------------------------------------
// Computed Properties
//----------------------------------------------------------------
const selectedAssessment = computed(() => {
  return assessments.value.find((a) => a.id == selectedAssessmentId.value);
});

const selectedAssessmentName = computed(() => {
  return selectedAssessment.value
    ? `${selectedAssessment.value.id}: ${selectedAssessment.value.name}`
    : "";
});

//----------------------------------------------------------------
// Data Fetching
//----------------------------------------------------------------
/**
 * Fetches the list of all available assessments to populate the dropdown.
 * Uses optimized endpoint that returns only basic fields (id, name, question_selection_mode).
 * Includes robust handling for different API response formats and filters out invalid data.
 */
const fetchAssessments = async () => {
  isLoading.value = true;
  clearMessage();
  try {
    const response = await api.admin.getAssessmentsBasic({
      limit: 100,
      offset: 0,
    });
    const data = extractResponseData(response);

    let rawAssessments = [];
    if (data) {
      rawAssessments = Array.isArray(data) ? data : data.assessments || [];
    }

    // Ensure every item in the list is a valid assessment object with an ID.
    assessments.value = rawAssessments.filter(
      (a) => a && typeof a === "object" && (a.id != null || a.id_hash != null),
    );

    if (assessments.value.length === 0) {
      setErrorMessage("No valid assessments found. Please create one first.");
    }
  } catch (err) {
    logError(err, "fetchAssessments");
    setErrorMessage(getErrorMessage(err, "Failed to fetch assessments."));
    assessments.value = [];
  } finally {
    isLoading.value = false;
  }
};

//----------------------------------------------------------------
// Core Logic
//----------------------------------------------------------------
/**
 * Validates user input and calls the API to generate assessment sessions.
 * On success, it clears the form and triggers `generateQuizLink`.
 */
const generateSessions = async () => {
  if (!selectedAssessmentId.value || !usernames.value) {
    setErrorMessage(
      "Please select an assessment and enter at least one username.",
    );
    return;
  }

  isLoading.value = true;
  clearMessage();
  sessions.value = [];
  generatedLink.value = "";

  try {
    // --- Extensive Frontend Validation ---
    // This provides immediate feedback to the user and prevents invalid requests to the server.
    const usernameList = usernames.value
      .split(",")
      .map((u) => u.trim())
      .filter(Boolean);
    if (usernameList.length === 0) {
      throw new Error("Please enter at least one valid username.");
    }
    if (new Set(usernameList).size !== usernameList.length) {
      throw new Error("Duplicate usernames are not allowed.");
    }

    // Validate each username or email format.
    const invalidEntries = usernameList.filter((u) => {
      if (u.includes("@")) {
        return !/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(u);
      }
      return !/^[a-zA-Z0-9._-]{2,50}$/.test(u);
    });

    if (invalidEntries.length > 0) {
      throw new Error(`Invalid entries found: ${invalidEntries.join(", ")}.`);
    }

    // --- API Call Preparation ---
    // The backend expects a numeric ID, so we must decode it if it's a hash.
    let assessmentId;
    if (isHashId(selectedAssessmentId.value)) {
      assessmentId = await decodeAssessmentId(selectedAssessmentId.value);
      if (!assessmentId)
        throw new Error("Failed to decode the selected assessment ID.");
    } else {
      assessmentId = parseInt(selectedAssessmentId.value, 10);
      if (isNaN(assessmentId)) throw new Error("Invalid assessment selected.");
    }

    const response = await api.admin.createSession({
      assessment_id: assessmentId,
      usernames: usernames.value, // API expects a comma-separated string.
    });

    const responseData = extractResponseData(response);
    if (!responseData) throw new Error("Invalid response from server.");

    // Process the results and display feedback.
    const rawSessions = responseData.sessions || [];
    sessions.value = await decodeSessionCodes(rawSessions);

    let feedbackMessage =
      responseData.message ||
      `Successfully generated ${sessions.value.length} session(s).`;
    if (responseData.warnings) {
      feedbackMessage += `\nWarnings: ${responseData.warnings}`;
    }
    setSuccessMessage(feedbackMessage);

    usernames.value = ""; // Clear form on success.

    // If sessions were created, proceed to generate the shareable quiz link.
    if (sessions.value.length > 0) {
      await generateQuizLink(assessmentId);
    }
  } catch (err) {
    logError(err, "generateSessions");
    setErrorMessage(getErrorMessage(err, "Failed to generate sessions."));
  } finally {
    isLoading.value = false;
  }
};

/**
 * Generates a generic, shareable link for the selected assessment.
 * This is called automatically after sessions are successfully created.
 */
const generateQuizLink = async (assessmentId) => {
  try {
    const response = await api.admin.generateLink({
      assessment_id: assessmentId,
    });
    generatedLink.value = extractResponseData(response)?.link;

    // Update the success message to confirm both actions are complete.
    setSuccessMessage(
      "Sessions and quiz link generated! View pending sessions in the Sessions List.",
    );

    // Show success notification
    showSuccess("Sessions generated successfully! View in Sessions List", {
      duration: 7000,
      actionText: "View Sessions",
      onAction: () => navigateTo("/sessions"),
    });
  } catch (err) {
    logError(err, "generateQuizLink");

    // Inform the user that the primary action succeeded even if this secondary one failed.
    setErrorMessage(
      "Sessions were created, but the quiz link could not be generated.",
    );
    generatedLink.value = "";
  }
};

//----------------------------------------------------------------
// UI Helpers
//----------------------------------------------------------------
const copyToClipboard = async () => {
  try {
    await navigator.clipboard.writeText(generatedLink.value);
    setSuccessMessage("Link copied to clipboard!");
  } catch (err) {
    logError(err, "copyToClipboard");
    setErrorMessage("Failed to copy link.");
  }
};

onMounted(() => {
  fetchAssessments();
});
</script>
