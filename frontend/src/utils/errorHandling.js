/**
 * Utility functions for handling API errors
 */
import { error as logErrorUtil, info } from "./logger";

/**
 * Extracts a detailed error message from an Axios error response.
 * @param {object} response - The error.response object from Axios.
 * @returns {string|null} The extracted error message or null if not found.
 */
const getErrorFromResponse = (response) => {
  if (!response?.data) return null;
  return response.data.detail || response.data.message || response.data.error;
};

/**
 * Provides a user-friendly message for common HTTP status codes.
 * @param {number} status - The HTTP status code.
 * @returns {string|null} A friendly error message or null.
 */
const getMessageForStatusCode = (status) => {
  switch (status) {
    case 400:
      return "Invalid request. Please check your input.";
    case 401:
      return "You need to be logged in to perform this action.";
    case 403:
      return "You don't have permission to perform this action.";
    case 404:
      return "The requested resource was not found.";
    case 500:
      return "Server error. Please try again later.";
    default:
      return null;
  }
};

/**
 * Extract a user-friendly error message from an API error.
 * @param {Error} error - The error object from Axios.
 * @param {string} defaultMessage - Default message to show if no specific error is found.
 * @returns {string} A user-friendly error message.
 */
export const getErrorMessage = (
  error,
  defaultMessage = "An error occurred. Please try again."
) => {
  if (!error) return defaultMessage;

  if (error.response) {
    const detail = getErrorFromResponse(error.response);
    if (detail) return detail;

    const statusMessage = getMessageForStatusCode(error.response.status);
    if (statusMessage) return statusMessage;

    return `Error ${error.response.status}: An unexpected error occurred.`;
  }

  if (error.request) {
    return "Network error. Please check your connection and try again.";
  }

  return error.message || defaultMessage;
};

/**
 * Log an error with additional context using the centralized logger
 * @param {Error} error - The error object
 * @param {string} context - Context information about where the error occurred
 */
export const logError = (error, context = "") => {
  const message = context ? `Error in ${context}` : "An error occurred";
  logErrorUtil(message, { error, context });
};

/**
 * Log quiz quit event with progress information
 * @param {string} quitType - Type of quit (route_leave, page_unload, component_unmount)
 * @param {object} progress - Quiz progress data
 */
export const logQuizQuit = (quitType, progress = {}) => {
  info(`Quiz quit via ${quitType}`, {
    quitType,
    questionsAttempted: progress.questionsAttempted || 0,
    correctAnswers: progress.correctAnswers || 0,
    finalScore: progress.finalScore || 0,
    sessionCode: progress.sessionCode || "unknown",
  });
};

/**
 * Log quiz submission error
 * @param {Error} error - The submission error
 * @param {string} context - Context of the submission attempt
 */
export const logQuizSubmissionError = (error, context = "quiz_submission") => {
  logErrorUtil(`Quiz submission failed in ${context}`, { error, context });
};

/**
 * Show confirmation popup for quiz quit with progress info
 * @param {object} progress - Quiz progress data
 * @returns {boolean} - User confirmation result
 */
export const confirmQuizQuit = (progress = {}) => {
  const { questionsAttempted = 0 } = progress;

  const message =
    questionsAttempted > 0
      ? `Are you sure you want to leave? Your quiz will be submitted with your current progress:\n\n• Questions answered: ${questionsAttempted}\n\nThis action cannot be undone.`
      : "Are you sure you want to leave? Your quiz session will be terminated and cannot be resumed.";

  const confirmed = confirm(message);

  if (confirmed) {
    info("User confirmed quiz quit", { questionsAttempted });
  } else {
    info("User cancelled quiz quit", { questionsAttempted });
  }

  return confirmed;
};
