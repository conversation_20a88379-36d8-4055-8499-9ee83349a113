<template>
  <div class="w-full h-full">
    <Radar :data="chartData" :options="chartOptions" />
  </div>
</template>

<script setup name="UserSkillsSpiderChart">
import { computed } from "vue";
import { Radar } from "vue-chartjs";
import {
  Chart as ChartJS,
  RadialLinearScale,
  PointElement,
  LineElement,
  Filler,
  Tooltip,
  Legend,
} from "chart.js";
import config from "../../config/globalConfig";

// Register Chart.js components
ChartJS.register(
  RadialLinearScale,
  PointElement,
  LineElement,
  Filler,
  Tooltip,
  Legend,
);

const props = defineProps({
  skills: {
    type: Array,
    required: true,
    default: () => [],
  },
  color: {
    type: String,
    default: "#8b5cf6", // Default purple color
  },
  backgroundColor: {
    type: String,
    default: "rgba(139, 92, 246, 0.2)", // Default purple with transparency
  },
  maxValue: {
    type: Number,
    default: 100,
  },
  maxSkills: {
    type: Number,
    default: 8,
  },
});

// Prepare chart data
const chartData = computed(() => {
  // Filter skills with data to avoid empty entries
  const validSkills = props.skills.filter((skill) => skill && skill.skill_name);

  // Get the number of skills to display based on props or default config
  const maxSkills = props.maxSkills || config.defaultMaxSkillCount;

  // Limit to top skills to avoid overcrowding
  const topSkills = validSkills.slice(0, maxSkills);

  const labels = topSkills.map((skill) => skill.skill_name);
  const data = topSkills.map((skill) => skill.accuracy_percentage || 0);

  return {
    labels,
    datasets: [
      {
        label: "Skill Accuracy",
        data,
        backgroundColor: props.backgroundColor,
        borderColor: props.color,
        borderWidth: 2.5,
        pointBackgroundColor: props.color,
        pointBorderColor: "#fff",
        pointHoverBackgroundColor: "#fff",
        pointHoverBorderColor: props.color,
        pointRadius: 5,
        pointHoverRadius: 8,
        borderJoinStyle: "round",
        tension: 0.1,
      },
    ],
  };
});

// Chart options
const chartOptions = computed(() => ({
  responsive: true,
  maintainAspectRatio: false,
  scales: {
    r: {
      angleLines: {
        color: "rgba(255, 255, 255, 0.2)",
        lineWidth: 1.5,
      },
      grid: {
        color: "rgba(255, 255, 255, 0.15)",
        circular: true,
      },
      pointLabels: {
        color: "rgba(255, 255, 255, 0.9)",
        font: {
          size: 14,
          weight: "bold",
          family: "'Inter', 'Helvetica', 'Arial', sans-serif",
        },
      },
      beginAtZero: true,
      min: 0,
      max: props.maxValue,
      suggestedMin: 0,
      suggestedMax: props.maxValue,
      ticks: {
        color: "rgba(255, 255, 255, 0.8)",
        backdropColor: "transparent",
        stepSize: 20,
        min: 0,
        max: props.maxValue,
        font: {
          size: 11,
        },
        showLabelBackdrop: false,
        z: 1,
      },
    },
  },
  plugins: {
    legend: {
      display: false,
    },
    tooltip: {
      backgroundColor: "rgba(30, 30, 30, 0.9)",
      titleColor: "#fff",
      bodyColor: "#fff",
      titleFont: {
        size: 14,
        weight: "bold",
      },
      bodyFont: {
        size: 13,
      },
      padding: 12,
      cornerRadius: 6,
      displayColors: false,
      callbacks: {
        title: (context) => {
          // Get the skill name from the label
          return context[0].label;
        },
        label: (context) => {
          // Get the skill data for this point
          const skillIndex = context.dataIndex;
          const skill = props.skills[skillIndex];

          if (!skill) return `Accuracy: ${context.raw}%`;

          return [
            `Accuracy: ${context.raw}%`,
            `Questions: ${skill.total_questions_answered || 0}`,
            `Correct: ${skill.correct_answers || 0}`,
          ];
        },
      },
    },
  },
}));
</script>
