/**
 * Composables Index
 *
 * Central export file for all reusable composables
 * This allows for clean imports like: import { useModal, useFormValidation } from '@/composables'
 */

// Modal and Dialog Management
export {
  useDetailedResultsModal,
} from "./useModal";

// Navigation Utilities
export { useNavigation } from "./useNavigation";

// UI State Management
export {
  useLoadingState,
  useSearch,
  usePagination,
  useTabs,
  useToggle,
} from "./useUIState";

// View-specific Composables
export { useSessions } from "./useSessions";
export { useSessionResults } from "./useSessionResults";
export { useAssessments } from "./useAssessments";
export { useAssessmentReport } from "./useAssessmentReport";
export { useSkillsForList } from "./useSkillsForList";
export { useSkillwiseReport } from "./useSkillwiseReport";
export { useUserReports } from "./useUserReports";
export { useUserSessions } from "./useUserSessions";

// Quiz-specific Composables
export { useFullscreenQuiz } from "./useFullscreenQuiz";
